APP_NAME=Rafiki
APP_ENV=local
APP_KEY=
APP_LOG_LEVEL=error
APP_DEBUG=false
APP_URL=https://rafiki.mx/api/public

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=rafiki
DB_USERNAME=rafiki
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

#digital_ocean
DO_SPACES_KEY=
DO_SPACES_SECRET=
DO_SPACES_ENDPOINT=https://nyc3.digitaloceanspaces.com
DO_SPACES_REGION=NYC3
DO_SPACES_BUCKET=rafiki-bucket

FRONTHOST=https://rafiki.mx/

#develop
CONEKTA_PUBLIC_KEY=
CONEKTA_PRIVATE_KEY=


#google key
GOOGLE_KEY=

#push_notifications
FCM_SERVER_KEY=
FCM_SENDER_ID=
FCM_PROTOCOL=http
