<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Auth
Route::post('admin/login', 'Auth\AuthController@loginAdmin');
Route::post('login', 'Auth\AuthController@login');
Route::post('register', 'UserController@store');
Route::post('register/check-email', 'UserController@checkMail');
Route::post('register/check-nickname', 'User<PERSON>ontroller@checkNickname');

Route::post('reset-password', 'Auth\ResetPasswordController@reset');
Route::post('reset-confirm', 'Auth\ResetPasswordController@resetConfirm');

Route::get('/email/verify/{id}/{hash}', 'Auth\VerificationController@verify')->name('verification.verify');
Route::post('/email/resend', 'Auth\VerificationController@resend')->name('verification.resend');
Route::get('get/resend/{id}', 'UserController@getResend');

Route::middleware('auth:api')->get('/user', function (Request $request) {
	return $request->user();
});

Route::group(['middleware' => 'auth:api'], function () {
	Route::post('app/logout', 'Auth\AuthController@logout');

	Route::group(['prefix' => 'report'], function() {
	    Route::get('raffles', 'ReportController@raffles');
	    Route::get('sales', 'ReportController@sales');
	    Route::get('winners', 'ReportController@winners');
	    Route::get('wallet-recharges', 'ReportController@recharges');
	});

	Route::get('user/permissions', 'UserController@getPermissions');
	Route::put('user/lock-time', 'UserController@updateLockTime');
	Route::put('user/unlock-time', 'UserController@cancelLockTime');
	Route::get('role/permissions', 'RoleController@getPermissions');
	Route::put('user/change-password-by-admin/{id}', 'UserController@changePasswordByAdmin');
	Route::post('user/change-password', 'UserController@changePasswordByUser');

	Route::get('profile', 'UserController@getProfile');
	Route::post('profile', 'UserController@profile');

	Route::get('raffle/admin', 'RaffleController@admin');
	Route::post('raffle/upload-photo', 'RaffleController@uploadPhoto');
	Route::get('raffle/record', 'RaffleController@record');
	Route::get('raffle/{id}/winner-detail', 'RaffleController@winnerDetail');
	Route::post('raffle/{raffle}/cancel', 'RaffleController@cancel');
	Route::post('raffle-winner/{raffleWinner}/update-address', 'RaffleWinnerController@updateAddress');
	Route::post('raffle-winner/{raffleWinner}/update-delivery-information', 'RaffleWinnerController@updateDeliveryInformation');
	Route::post('raffle-winner/{raffleWinner}/update-prize-status', 'RaffleWinnerController@updatePrizeStatus');
	Route::post('raffle/{raffle}/digital-prize', 'RaffleDigitalPrizeController@addDigitalPrize');
	Route::post('raffle/{raffle}/digital-prize-evidence', 'RaffleDigitalPrizeEvidenceController@addEvidence');

	Route::post('application/{application}/upload-id-front', 'ApplicationController@uploadIdFront');
	Route::post('application/{application}/upload-id-back', 'ApplicationController@uploadIdBack');
	Route::post('application/{application}/upload-bank-information', 'ApplicationController@uploadBankInformation');
	Route::get('application/status', 'ApplicationController@getStatus');
	Route::post('application/{id}/rfc', 'ApplicationController@updateRFC');

	Route::resource('raffle', 'RaffleController')->except(['index', 'show']);
	Route::resource('cancel-report', 'CancelReportController')->only(['index', 'show']);
	Route::resource('category', 'CategoryController')->except(['index']);

	Route::resource('raffler-payment', 'RafflerPaymentController')->except(['store']);
	Route::post('raffler-payment/generate-payment/{rafflerPayment}', 'RafflerPaymentController@generatePayment');
	Route::post('raffler-payment/{rafflerPayment}/upload-evidence', 'RafflerPaymentController@uploadEvidence');

	Route::put('ticket/lock/{id}', 'TicketController@lock');
	Route::put('ticket/unlock/{id}', 'TicketController@unlock');
	Route::post('ticket/random', 'TicketController@random');
	Route::post('ticket/payment/{id}/order', 'TicketController@order');
	//Route::post('ticket/payment/{id}', 'TicketController@payment');
    Route::post('wallet/deposit', 'BalanceController@deposit');
    Route::get('wallet', 'BalanceController@wallet');

    Route::get('notification','NotificationController@index');
    Route::get('notification/show-all','NotificationController@showAll');
    Route::put('notification/read/{id}','NotificationController@read');
    Route::put('notification/read-all','NotificationController@readAll');
    Route::post('notification/test','NotificationController@test');

	Route::post('wallet/deposit', 'BalanceController@deposit');
	Route::get('wallet', 'BalanceController@wallet');

	Route::post('address/{id}', 'AddressController@update');

	Route::apiResources([
		'role' => 'RoleController',
		'client-user' => 'UserController',
		'admin-user'  => 'AdminUserController',
		'permission'  => 'PermissionController',
		'subcategory' => 'SubcategoryController',
		'application' => 'ApplicationController',
		'card' => 'CardController',
		'raffle-report' => 'RaffleReportController',
		'address' => 'AddressController',
		'refund'  => 'RefundController'
	]);
});

//Not login services
Route::get('raffle/results', 'RaffleController@results');
Route::get('raffle', 'RaffleController@index');
Route::get('raffle/{id}', 'RaffleController@show');
Route::get('category', 'CategoryController@index');
Route::get('category/{id}/subcategories', 'CategoryController@subcategories');
Route::get('catalog/state', 'CatalogController@states');
