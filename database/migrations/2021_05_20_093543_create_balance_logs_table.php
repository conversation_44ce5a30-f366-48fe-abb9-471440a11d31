<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBalanceLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('balance_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('log');
            $table->text('data');
            $table->enum('status',['success','error']);
            
            $table->unsignedBiginteger('balance_id')->nullable();
            $table->unsignedBiginteger('card_id')->nullable();
            $table->unsignedBiginteger('user_id')->nullable();

            $table->timestamps();
            
            $table->foreign('balance_id')
                ->references('id')
                ->on('balances');
            
            $table->foreign('card_id')
                ->references('id')
                ->on('cards');

            $table->foreign('user_id')
                ->references('id')
                ->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('balance_logs');
    }
}
