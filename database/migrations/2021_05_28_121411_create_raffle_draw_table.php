<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRaffleDrawTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffle_draws', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBiginteger('raffle_id');
            $table->unsignedBiginteger('winner_ticket_id')->nullable();
            $table->datetime('start');
            $table->datetime('end')->nullable();
            $table->boolean('finished')->default(0);

            $table->foreign('raffle_id')->references('id')->on('raffles');
            $table->foreign('winner_ticket_id')->references('id')->on('tickets');


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffle_draws');
    }
}
