<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketPaymentOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ticket_payment_order', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBiginteger('ticket_id');
            $table->unsignedBiginteger('payment_order_id');
            $table->timestamps();

            $table->foreign('ticket_id')
                ->references('id')
                ->on('tickets');
            
            $table->foreign('payment_order_id')
                ->references('id')
                ->on('payment_orders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ticket_payment_order');
    }
}
