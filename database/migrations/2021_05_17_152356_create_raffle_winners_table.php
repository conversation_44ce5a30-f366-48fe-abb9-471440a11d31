<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRaffleWinnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffle_winners', function (Blueprint $table) {
            $table->bigIncrements('id');
			$table->unsignedBigInteger('winner_id');
			$table->unsignedBigInteger('raffle_id');
			$table->unsignedBigInteger('ticket_id');
			$table->string('delivery_company')->nullable();
			$table->string('track_id')->nullable();
			$table->dateTime('estimated_delivery_date')->nullable();
			$table->boolean('received_prize')->default(0);
			$table->string('evidence_url')->nullable();
            $table->timestamps();

			$table->foreign('winner_id')->references('id')->on('users');
			$table->foreign('raffle_id')->references('id')->on('raffles');
			$table->foreign('ticket_id')->references('id')->on('tickets');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffle_winners');
    }
}
