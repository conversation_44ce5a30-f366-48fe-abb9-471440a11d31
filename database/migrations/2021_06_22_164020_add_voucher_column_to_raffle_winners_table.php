<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddVoucherColumnToRaffleWinnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('raffle_winners', function (Blueprint $table) {
            $table->string('delivery_voucher_url')->nullable()->after('estimated_delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('raffle_winners', function (Blueprint $table) {
            $table->dropColumn('delivery_voucher_url');
        });
    }
}
