<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRaffleIdToBalancesTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::table('balances', function (Blueprint $table) {
			$table->unsignedBigInteger('raffle_id')->after('card_id')->nullable();
			$table->string('description')->after('raffle_id')->nullable();
			$table->foreign('raffle_id')->references('id')->on('raffles');
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::table('balances', function (Blueprint $table) {
			$table->dropForeign(['raffle_id']);
			$table->dropColumn('raffle_id');
			$table->dropColumn('description');
		});
	}
}
