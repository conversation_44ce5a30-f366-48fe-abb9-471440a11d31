<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterRaffleWinnerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('raffle_winners', function (Blueprint $table) {
            $table->unsignedBigInteger('address_id')->after('ticket_id')->nullable();
            $table->datetime('reception_date')->after('received_prize')->nullable();

            $table->foreign('address_id')->references('id')->on('addresses');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('raffle_winners', function (Blueprint $table) {
            $table->dropColumn('reception_date'); 
            $table->dropForeign(['address_id']); 
            $table->dropColumn('address_id'); 
        });
    }
}
