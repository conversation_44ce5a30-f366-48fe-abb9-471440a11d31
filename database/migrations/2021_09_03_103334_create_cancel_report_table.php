<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCancelReportTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cancel_reports', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('tickets_sold');
            $table->decimal('commission');
            $table->enum('status', ['success', 'error']);
            $table->text('data');
            $table->text('error')->nullable();
            $table->unsignedBiginteger('raffle_id');
            $table->unsignedBiginteger('user_id');
            $table->unsignedBiginteger('card_id')->nullable();
            $table->timestamps();

            $table->foreign('raffle_id')
                ->references('id')
                ->on('raffles');
            
            $table->foreign('user_id')
                ->references('id')
                ->on('users');
            
            $table->foreign('card_id')
                ->references('id')
                ->on('cards');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cancel_reports');
    }
}
