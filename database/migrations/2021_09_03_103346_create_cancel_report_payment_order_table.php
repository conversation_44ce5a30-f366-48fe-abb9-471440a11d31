<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCancelReportPaymentOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('cancel_report_payment_order', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->unsignedBiginteger('cancel_report_id');
            $table->unsignedBiginteger('payment_order_id');

            $table->foreign('cancel_report_id')
                ->references('id')
                ->on('cancel_reports');
            
            $table->foreign('payment_order_id')
                ->references('id')
                ->on('payment_orders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('cancel_report_payment_order');
    }
}
