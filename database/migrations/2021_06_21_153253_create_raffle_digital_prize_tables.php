<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRaffleDigitalPrizeTables extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('raffle_digital_prizes', function (Blueprint $table) {
			$table->bigIncrements('id');
			$table->unsignedBiginteger('raffle_id');
			$table->text('instructions');
			$table->string('redeem_code')->nullable();
			$table->string('external_link')->nullable();
			$table->string('url_instructions')->nullable();
			$table->timestamps();

			$table->foreign('raffle_id')->references('id')->on('raffles');
		});

		Schema::create('raffle_digital_prize_evidences', function (Blueprint $table) {
			$table->bigIncrements('id');
			$table->unsignedBiginteger('raffle_id');
			$table->unsignedBiginteger('user_id');
			$table->enum('type', ['external', 'instructions', 'file_instructions', 'redeem_code']);
			$table->timestamps();

			$table->foreign('raffle_id')->references('id')->on('raffles');
			$table->foreign('user_id')->references('id')->on('users');
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('raffle_digital_prizes');
		Schema::dropIfExists('raffle_digital_prize_evidences');
	}
}
