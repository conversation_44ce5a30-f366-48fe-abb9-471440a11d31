<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRafflePhotosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffle_photos', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('url_image');
            $table->boolean('default');
            $table->unsignedBiginteger('raffle_id');
            $table->timestamps();

            $table->foreign('raffle_id')
                ->references('id')
                ->on('raffles');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffle_photos');
    }
}
