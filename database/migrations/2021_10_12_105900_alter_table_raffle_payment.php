<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableRafflePayment extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('raffler_payments', function (Blueprint $table) {
            $table->decimal('commission_iva')->after('commission_amount');
            $table->decimal('isr_amount')->after('commission_iva');
            $table->decimal('isr_percentage')->after('isr_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('raffler_payments', function (Blueprint $table) {
            $table->dropColumn('commission_iva');
            $table->dropColumn('isr_amount');
            $table->dropColumn('isr_percentage');
        });
    }
}
