<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRafflesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffles', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name');
            $table->string('brand')->nullable();
            $table->string('model')->nullable();
            $table->text('description')->nullable();
            $table->unsignedBiginteger('cat_state_id');
            $table->text('city')->nullable();
            $table->datetime('start');
            $table->datetime('finish');
            $table->decimal('prize_value', 12, 2);
            $table->integer('num_tickets')->unsigned();
            $table->decimal('ticket_price', 12, 2);
            $table->decimal('suggested_value', 12, 2)->nullable();
            $table->boolean('active');
            $table->enum('product_type',['Physical','Digital','Service']);
            $table->enum('prize_type',['New','Used'])->nullable();
            $table->unsignedBiginteger('user_id');
            $table->timestamps();

            $table->unsignedBiginteger('subcategory_id');

            $table->foreign('cat_state_id')
                ->references('id')
                ->on('cat_states');

            $table->foreign('user_id')
                ->references('id')
                ->on('users');

            $table->foreign('subcategory_id')
                ->references('id')
                ->on('subcategories');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffles');
    }
}
