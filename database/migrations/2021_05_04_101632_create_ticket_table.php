<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tickets', function (Blueprint $table) {
			$table->bigIncrements('id');
			$table->string('number');
			$table->boolean('locked');
			$table->datetime('locked_finish')->nullable();
			$table->datetime('buy_date')->nullable();
			$table->boolean('fast_purchase')->nullable();
			$table->enum('payment_type', ['Conekta', 'Points'])->nullable();
			$table->enum('status', ['Free', 'Locked', 'Sold']);
			$table->unsignedBiginteger('raffle_id');
			$table->unsignedBiginteger('buyer_id')->nullable();
			$table->timestamps();

			$table->foreign('raffle_id')
				->references('id')
				->on('raffles');

			$table->foreign('buyer_id')
				->references('id')
				->on('users');
		});
	}

	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::dropIfExists('tickets');
	}
}
