<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('log');
            $table->text('data');
            $table->enum('status',['success','error']);
            
            $table->unsignedBiginteger('raffle_id');
            $table->unsignedBiginteger('payment_order_id')->nullable();
            $table->unsignedBiginteger('card_id')->nullable();

            $table->timestamps();

            
            $table->foreign('raffle_id')
                ->references('id')
                ->on('raffles');
            
            $table->foreign('payment_order_id')
                ->references('id')
                ->on('payment_orders');
            
            $table->foreign('card_id')
                ->references('id')
                ->on('cards');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_logs');
    }
}
