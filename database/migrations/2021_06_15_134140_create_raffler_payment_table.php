<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRafflerPaymentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffler_payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('ticket_amount');
            $table->decimal('commission_amount');
            $table->decimal('raffler_amount');
            $table->string('evidence_url')->nullable();
            $table->enum('status', ['pending', 'rejected', 'accepted'])->default('pending');
            $table->datetime('cancelation_date')->nullable();
            $table->datetime('accept_date')->nullable();
            $table->text('comments')->nullable();
            $table->unsignedBiginteger('raffle_id');
            $table->unsignedBiginteger('admin_id')->nullable();
            $table->unsignedBiginteger('raffler_id');
            $table->timestamps();

            $table->foreign('raffle_id')
                ->references('id')
                ->on('raffles');
            
            $table->foreign('admin_id')
                ->references('id')
                ->on('users');
            
            $table->foreign('raffler_id')
                ->references('id')
                ->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffler_payments');
    }
}
