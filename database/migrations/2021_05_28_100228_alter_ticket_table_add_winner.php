<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTicketTableAddWinner extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->boolean('winner')->after('buyer_id')->default(0);
            $table->integer('selection_number')->after('winner')->nullable();
            $table->datetime('selection_time')->after('selection_number')->nullable();
        });
    }
        

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn('winner');
            $table->dropColumn('selection_number');
            $table->dropColumn('selection_time');
        });
    }
}
