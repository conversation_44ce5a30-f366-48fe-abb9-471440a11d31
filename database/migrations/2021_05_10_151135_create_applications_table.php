<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateApplicationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applications', function (Blueprint $table) {
            $table->bigIncrements('id');
			$table->unsignedBiginteger('user_id');
			$table->unsignedBiginteger('admin_id')->nullable();
			$table->string('url_id_front');
			$table->string('url_id_back')->nullable();
			$table->string('url_bank_information');
			$table->boolean('approval')->default(0);
			$table->dateTime('approval_date')->nullable();
			$table->text('rejection_reason')->nullable();
            $table->timestamps();

			$table->foreign('user_id')->references('id')->on('users');
			$table->foreign('admin_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applications');
    }
}
