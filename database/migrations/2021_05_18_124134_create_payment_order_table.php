<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePaymentOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('num_tickets');
            $table->integer('payment_total');
            $table->decimal('commission');
            $table->string('transaction')->nullable();
            $table->enum('payment_type',['Conekta','Rafiki','Points']);
            $table->enum('status',['success','error']);
            $table->unsignedBiginteger('raffle_id');
            $table->unsignedBiginteger('card_id')->nullable();
            $table->unsignedBiginteger('buyer_id');
            $table->timestamps();

            $table->foreign('raffle_id')->references('id')->on('raffles');
            $table->foreign('card_id')->references('id')->on('cards');
            $table->foreign('buyer_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_orders');
    }
}
