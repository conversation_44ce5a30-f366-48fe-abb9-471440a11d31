<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRaffleReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('raffle_reports', function (Blueprint $table) {
            $table->bigIncrements('id');
			$table->unsignedBigInteger('raffle_id');
			$table->unsignedBigInteger('user_id');
			$table->unsignedBigInteger('admin_id')->nullable();
			$table->enum('status', ['pending', 'rejected', 'accepted'])->default('pending');
			$table->text('comments');
			$table->text('admin_comments')->nullable();
			$table->dateTime('reviewed_at')->nullable();
            $table->timestamps();

			$table->foreign('raffle_id')->references('id')->on('raffles');
			$table->foreign('user_id')->references('id')->on('users');
			$table->foreign('admin_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('raffle_reports');
    }
}
