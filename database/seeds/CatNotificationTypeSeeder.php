<?php

use Illuminate\Database\Seeder;
use App\Model\CatNotificationType;

class CatNotificationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $data = [
			"Solicitud Aprobada",
			"Solicitud Rechazada",
			"Ganador de la Rifa",
			"Sorteo de la Rifa",
			"Ganador de la Rifa - Actualización de dirección",
			"Rifador - Actualización de información de entrega",
			"Ganador de la Rifa - Recibió el premio",
			"Rifador - Actualización de instrucciones del premio digital",
			"Administrador - Subió comprobante de pago",
			"Rifador - Rifa cancelada",
			"Cliente - Rifa cancelada",
		];

		Schema::disableForeignKeyConstraints();
        CatNotificationType::truncate();

		foreach ($data as $pName) {
			$object = new CatNotificationType;
			$object->name = $pName;
			$object->save();
		}


		Schema::enableForeignKeyConstraints();
    }
}
