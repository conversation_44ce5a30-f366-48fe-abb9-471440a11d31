<?php

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\User;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
    	Permission::truncate();

		$permissionA = Permission::create(['name' => 'Administrador']);
        $permissionR = Permission::create(['name' => 'Rifante']);
        $permissionC = Permission::create(['name' => 'Comprador']);
		
		Schema::enableForeignKeyConstraints();

		$admin = Role::where('name',"Administrador")->first();

    	if(!$admin){
    		$admin = Role::create(['guard_name' => 'web', 'name' => 'Administrador'])->givePermissionTo(Permission::all());
    	}

        $raffler = Role::where('name',"Rifante")->first();

        if(!$raffler){
            $raffler = Role::create(['guard_name' => 'web', 'name' => 'Rifante'])->givePermissionTo([$permissionR,$permissionC]);
        }

        $client = Role::where('name',"Comprador")->first();

        if(!$client){
            $client = Role::create(['guard_name' => 'web', 'name' => 'Comprador'])->givePermissionTo([$permissionC]);
        }

    }
}
