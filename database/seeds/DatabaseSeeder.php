<?php

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
	/**
	 * Seed the application's database.
	 *
	 * @return void
	 */
	public function run()
	{
		$this->call(PermissionSeeder::class);
		$this->call(CatNotificationTypeSeeder::class);

		$adminUser = App\User::where('email', '<EMAIL>')->first();

		if (!$adminUser) {
			$users = factory(App\User::class, 1)->create([
				'email' => '<EMAIL>'
			])->each(function ($user) {
				$role = Role::where('name', 'Administrador')->first();

				if ($role != null) {
					$user->assignRole($role->name);
				}
			});
		}

		$users = factory(App\User::class, 10)->create()
		->each(function ($user) {
			$role = Role::where('name', 'Administrador')->first();

			if ($role != null) {
				$user->assignRole($role->name);
			}
		});

		$users = factory(App\User::class, 10)->create()
			->each(function ($user) use ($adminUser) {
				$role = Role::where('name', 'Rifante')->first();

				if ($role != null) {
					$user->assignRole($role->name);
				}

				$application = factory(App\Model\Application::class, 1)->create([
					'user_id'  => $user->id,
					'admin_id' => $adminUser->id
				]);

				$raffle = factory(App\Model\Raffle::class, 1)->create([
					'user_id'  => $user->id
				])->each(function ($raffle) {
					$ticket = factory(App\Model\Ticket::class, 20)->create([
						'raffle_id' => $raffle->id,
					]);

					$ticket = App\Model\Ticket::where('raffle_id', $raffle->id)
											  ->get()
											  ->random(1)
											  ->first();
					
					$raffleWinner = factory(App\Model\RaffleWinner::class, 1)->create([
						'raffle_id' => $ticket->raffle_id,
						'winner_id' => $ticket->buyer_id,
						'ticket_id' => $ticket->id,
					]);
				});
			});

			$users = factory(App\User::class, 10)->create()
			->each(function ($user) use ($adminUser) {
				$role = Role::where('name', 'Comprador')->first();

				if ($role != null) {
					$user->assignRole($role->name);
				}

				$application = factory(App\Model\Application::class, 1)->create([
					'user_id' => $user->id,
					'admin_id' => $adminUser->id
				]);
			});

		$categories = factory(App\Model\Category::class, 20)->create()->each(function ($category) {
			$subcategories = factory(App\Model\Subcategory::class, 3)->create([
				'category_id' => $category->id
			]);
		});

		$reports = factory(App\Model\RaffleReport::class, 20)->create();
	}
}
