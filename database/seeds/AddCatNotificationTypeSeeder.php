<?php

use Illuminate\Database\Seeder;
use App\Model\CatNotificationType;

class AddCatNotificationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();

        $data = [
            "Rifador - Se agotaron los boletos",
            "Cliente - Se agotaron los boletos",
            "Rifador - Próxima a terminar",
            "Cliente - Próxima a terminar"
        ];

        foreach ($data as $pName) {
            $object = new CatNotificationType;
            $object->name = $pName;
            $object->save();
        }

        Schema::enableForeignKeyConstraints();
    }
}
