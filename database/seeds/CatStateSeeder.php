<?php

use Illuminate\Database\Seeder;
use App\Model\CatState;

class CatStateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
		$data = [
			"Aguascalientes",
			"Baja California",
			"Baja California Sur",
			"Campeche",
			"Coahuila de Zaragoza",
			"Colima",
			"Chiapas",
			"Chihuahua",
			"CDMX",
			"Durango",
			"Guanajuato",
			"Guerrero",
			"Hidalgo",
			"Jalisco",
			"México",
			"Michoacán de Ocampo",
			"Morelos",
			"Nayarit",
			"Nuevo León",
			"Oaxaca",
			"Puebla",
			"Querétaro",
			"Quintana Roo",
			"San Luis Potosí",
			"Sinaloa",
			"Sonora",
			"Tabasco",
			"Tamaulipas",
			"Tlaxcala",
			"Veracruz de Ignacio de la Llave",
			"Yucatán",
			"Zacatecas"
		];

		Schema::disableForeignKeyConstraints();
        CatState::truncate();

		foreach ($data as $stateName) {
			$state = new CatState;
			$state->name = $stateName;
			$state->save();
		}

		Schema::enableForeignKeyConstraints();
    }
}

