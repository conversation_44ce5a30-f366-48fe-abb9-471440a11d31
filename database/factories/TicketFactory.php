<?php

use App\Model\Ticket;
use App\User;
use App\Model\Raffle;
use Faker\Generator as Faker;	

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Ticket::class, function (Faker $faker) {
	$buyerUser = User::selectRaw('users.*')
	->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
	->join('roles as r', 'r.id', 'mhr.role_id')
	->where('r.name', 'Comprador')
	->get()
	->random(1)->first();

	$raffle = Raffle::all()->random(1)->first();

	return [
		'number'        => $faker->uuid,
		'locked'        => $faker->boolean(),
		'locked_finish' => date('Y-m-t'),
		'buy_date'      => date('Y-m-d'),
		'fast_purchase' => $faker->boolean(),
		'payment_type'  => $faker->boolean() ? 'Conekta' : 'Rafiki',
		'status'        => $faker->boolean() ? 'Free' : 'Sold',
		'raffle_id'     => $raffle->id,
		'buyer_id'      => $buyerUser->id
	];
});