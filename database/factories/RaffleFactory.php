<?php

use App\Model\Raffle;
use App\User;
use App\Model\CatState;
use App\Model\Subcategory;
use Faker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Raffle::class, function (Faker $faker) {
	$clientUser = User::selectRaw('users.*')
		->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
		->join('roles as r', 'r.id', 'mhr.role_id')
		->whereIn('r.name', ['Rifante'])
		->get()
		->random(1)->first();

	$state = CatState::all()->random(1)->first();
	$subcategory = Subcategory::all()->random(1)->first();

	return [
		'name'             => $faker->text(100),
		'brand'            => $faker->text(100),
		'model'            => $faker->year(),
		'description'      => $faker->text(),
		'cat_state_id'     => $state->id,
		'city'             => $faker->city,
		'start'            => now(),
		'finish'           => date('Y-m-t'),
		'prize_value'      => $faker->numberBetween(1, 200),
		'num_tickets'      => $faker->numberBetween(1, 200),
		'ticket_price'     => $faker->numberBetween(1, 200),
		'suggested_value'  => $faker->numberBetween(1, 200),
		'active'           => $faker->boolean(),
		'canceled'         => $faker->boolean(),
		'product_type'     => $faker->boolean() ? 'Physical' : 'Digital',
		'prize_type'       => $faker->boolean() ? 'New' : 'Used',
		'subcategory_id'   => $subcategory->id,
		'user_id'          => $clientUser->id,
	];
});