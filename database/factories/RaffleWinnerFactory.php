<?php

use App\Model\RaffleWinner;
use App\User;
use App\Model\Raffle;
use App\Model\Ticket;
use Faker\Generator as Faker;	

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(RaffleWinner::class, function (Faker $faker) {
	$winnerTicket = Ticket::all()->random(1)->first();

	return [
		'winner_id'               => $winnerTicket->buyer_id,
		'raffle_id'               => $winnerTicket->raffle_id,
		'ticket_id'               => $winnerTicket->id,
		'delivery_company'        => $faker->company,
		'track_id'                => $faker->uuid,
		'estimated_delivery_date' => date('Y-m-t'),
		'received_prize'          => $faker->boolean(),
		'evidence_url'            => $faker->imageUrl(),
	];
});