<?php

use App\Model\Application;
use App\User;
use Faker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Application::class, function (Faker $faker) {
	$adminUser = User::selectRaw('users.*')
		->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
		->join('roles as r', 'r.id', 'mhr.role_id')
		->where('r.name', 'Administrador')
		->get()
		->random(1)->first();

	$clientUser = User::selectRaw('users.*')
		->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
		->join('roles as r', 'r.id', 'mhr.role_id')
		->whereIn('r.name', ['Rifante', 'Comprador'])
		->get()
		->random(1)->first();

	return [
		'url_id_front'         => $faker->imageUrl(),
		'url_id_back'          => $faker->imageUrl(),
		'url_bank_information' => $faker->imageUrl(),
		'approval'             => $faker->boolean(),
		'approval_date'        => now(),
		'rejection_reason'      => $faker->text(),
		'user_id'              => $clientUser->id,
		'admin_id'			   => $adminUser->id
	];
});