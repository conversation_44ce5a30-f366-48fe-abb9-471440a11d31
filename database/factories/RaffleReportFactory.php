<?php

use App\Model\RaffleReport;
use App\User;
use App\Model\Raffle;
use Faker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(RaffleReport::class, function (Faker $faker) {
	$raffle = Raffle::all()->random(1)->first();

	$adminUser = User::selectRaw('users.*')
		->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
		->join('roles as r', 'r.id', 'mhr.role_id')
		->where('r.name', 'Administrador')
		->get()
		->random(1)->first();

	$clientUser = User::selectRaw('users.*')
		->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
		->join('roles as r', 'r.id', 'mhr.role_id')
		->whereIn('r.name', ['Rifante', 'Comprador'])
		->get()
		->random(1)->first();

	return [
		'raffle_id'		 => $raffle->id,
		'user_id'        => $clientUser->id,
		'admin_id'       => $adminUser->id,
		'status'         => $faker->boolean() ? 'rejected' : 'accepted',
		'comments'       => $faker->text(),
		'admin_comments' => $faker->text(),
		'reviewed_at'    => date('Y-m-d H:i:s'),
	];
});
