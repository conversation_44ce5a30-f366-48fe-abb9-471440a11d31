<?php

use App\Model\Category;
use App\Model\Subcategory;
use Faker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Subcategory::class, function (Faker $faker) {
	return [
		'category_id' => Category::all()->random(1)->first()->id,
		'name'        => $faker->name,
		'description' => $faker->text(200),
		'active'      => $faker->boolean(50)
	];
});
