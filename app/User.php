<?php

namespace App;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use App\Model\Device;
use App\Model\Card;
use App\Notifications\VerifyEmail;
use App\Notifications\CustomResetPassword;

use App\Model\PaymentOrder;
use App\Model\Balance;

use Illuminate\Support\Facades\Log;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, Notifiable;
    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'last_name',
		'email',
		'nickname',
		'password',
        'url_photo',
		'facebook_id',
		'apple_id',
		'conekta_source_id',
		'active',
    ];

    protected $appends = ['role'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token','conekta_source_id'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'ticket_locked_finish' => 'datetime',
    ];

    public function cards()
    {
        return $this->hasMany(Card::class)->active();
    }

    public function devices()
    {
        return $this->hasMany(Device::class)->active();
    }

    public function getRoleAttribute(){
        $role = ($this->roles && count($this->roles) > 0) ? $this->roles[0]->name : '';

        unset($this->roles); 
        return $role;
    }

    public function getRafikiAttribute(){
        $balances = Balance::select([
                'id',
                'amount',
                'created_at',
                ])
                ->where('user_id', $this->id)
                ->get();

        $balances_sum = $balances->sum('amount');

        $payments = PaymentOrder::select([
                'id',
                'num_tickets',
                'payment_total',
                'created_at',
                ])
                ->where('buyer_id', $this->id)
                ->where('payment_type', 'Rafiki')
                ->where('status', 'success')
                ->get();

        $payments_sum = $payments->sum('payment_total');

        $total = $balances_sum - $payments_sum;

        return $total;
    }

    public function sendEmailVerificationNotification()
    {
        $this->notify(new VerifyEmail);
    }

    public function sendPasswordResetNotification($token)
    {   
        $this->notify(new CustomResetPassword($token,$this->email));
    }

}
