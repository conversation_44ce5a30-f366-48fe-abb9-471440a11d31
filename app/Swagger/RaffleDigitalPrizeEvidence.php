<?php

/**
 * @OA\Schema(schema="RaffleDigitalPrizeEvidence", type="object")
 */
class RaffleDigitalPrizeEvidence
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="user_id", type="number")
	 */
	protected $user_id;

	/**
	 * @OA\Property(property="type", type="string", example="external | instructions | file_instructions | redeem_code")
	 */
	protected $type;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}

/**
 * @OA\Schema(schema="RaffleDigitalPrizeEvidenceAdd", type="object", required={"type"})
 */
class RaffleDigitalPrizeEvidenceAdd
{
	/**
	 * @OA\Property(property="type", type="string")
	 */
	protected $type;
}
