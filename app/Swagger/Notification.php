<?php

/**
 * @OA\Schema(schema="NotificationSave", type="object")
 */
class NotificationSave
{

    /**
     * @OA\Property(property="title", type="string")
     */
    protected $title;

    /**
     * @OA\Property(property="message", type="string")
     */
    protected $message;

    /**
     * @OA\Property(property="data", type="number")
     */
    protected $data;

    /**
     * @OA\Property(property="user_id", type="number")
     */
    protected $user_id; 

    /**
     * @OA\Property(property="player_id", type="number")
     */
    protected $player_id; 

    /**
     * @OA\Property(property="readed", type="number")
     */
    protected $readed; 

    /**
     * @OA\Property(property="created_at", type="date")
     */
    protected $created_at; 

    /**
     * @OA\Property(property="cat_notification_type_id", type="number")
     */
    protected $cat_notification_type_id;  
}

/**
 * @OA\Schema(
 *     schema="Notification",
 * allOf={
 *   @OA\Schema(
 *       @OA\Property(property="id", type="number"),
 *    ),
 *    @OA\Schema(ref="#/components/schemas/NotificationSave"),
 *    @OA\Schema(
 *      @OA\Property(
 *          property="type",
 *          type="object",
 *          ref="#/components/schemas/Catalog"
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(
 *     schema="NotificationPaginated",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Paginated"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="data",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/Notification")
 *       ),
 *    ),
 * }
 * )
 */

