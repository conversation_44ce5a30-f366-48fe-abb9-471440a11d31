<?php


/**
 * @OA\Schema(schema="PaymentOrder", type="object")
 */
class PaymentOrder
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="payment_total", type="number")
	 */
	protected $payment_total;

	/**
	 * @OA\Property(property="num_tickets", type="number")
	 */
	protected $num_tickets;

	/**
	 * @OA\Property(property="commission", type="number")
	 */
	protected $commission;

	/**
	 * @OA\Property(property="payment_type", type="string", example="Conekta | Rafiki | Points")
	 */
	protected $payment_type;

	/**
	 * @OA\Property(property="status", type="string" , example=" success | error")
	 */
	protected $status;

	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="buyer_id", type="number")
	 */
	protected $buyer_id;

	/**
	 * @OA\Property(property="card_id", type="number")
	 */
	protected $card_id;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}
