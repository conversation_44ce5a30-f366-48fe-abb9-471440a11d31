<?php


/**
 * @OA\Schema(schema="CardSave", type="object")
 */
class CardSave
{
    /**
     * @OA\Property(property="alias", type="string")
     */
    protected $alias;

     /**
     * @OA\Property(property="number", type="string")
     */
    protected $number;

    /**
     * @OA\Property(property="token", type="string")
     */
    protected $token;

    /**
     * @OA\Property(property="source_id", type="string")
     */
    protected $source_id;
    
    /**
     * @OA\Property(property="type", type="string")
     */
    protected $type;

    /**
     * @OA\Property(property="active", type="boolean")
     */
    protected $active;
  
}


/**
 * @OA\Schema(
 *     schema="Card",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/CardSave"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="id",
 *          type="number",
 *       ),
 *       @OA\Property(
 *          property="number_formatted",
 *          type="string",
 *       ),
 *       @OA\Property(
 *          property="type_formatted",
 *          type="string",
 *       ),
 *    ),
 * }
 * )
 */


