<?php

/**
 * @OA\Schema(schema="RaffleWinner", type="object")
 */
class RaffleWinner
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="winner_id", type="number")
	 */
	protected $winner_id;

	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="ticket_id", type="number")
	 */
	protected $ticket_id;

	/**
	 * @OA\Property(property="address_id", type="number")
	 */
	protected $address_id;

	/**
	 * @OA\Property(property="delivery_company", type="string")
	 */
	protected $delivery_company;

	/**
	 * @OA\Property(property="estimated_delivery_date", type="string", format="date-time")
	 */
	protected $estimated_delivery_date;

	/**
	 * @OA\Property(property="received_prize", type="number")
	 */
	protected $received_prize;

	/**
	 * @OA\Property(property="reception_date", type="string", format="date-time")
	 */
	protected $reception_date;

	/**
	 * @OA\Property(property="evidence_url", type="string")
	 */
	protected $evidence_url;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;

	/**
     *       @OA\Property(
     *          property="winner",
     *          type="object",
     *          ref="#/components/schemas/RaffleWinnerUser"
     *       )
     */

	/**
     *       @OA\Property(
     *          property="ticket",
     *          type="object",
     *          ref="#/components/schemas/RaffleWinnerTicket"
     *       )
     */

	/**
     *       @OA\Property(
     *          property="address",
     *          type="object",
     *          ref="#/components/schemas/Address"
     *       )
     */
}

/**
 * @OA\Schema(schema="RaffleWinnerUser", type="object")
 */
class RaffleWinnerUser
{

	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="name", type="string")
	 */
	protected $name;
}

/**
 * @OA\Schema(schema="RaffleWinnerTicket", type="object")
 */
class RaffleWinnerTicket
{

	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="number", type="string")
	 */
	protected $number;
}

/**
 * @OA\Schema(schema="RaffleWinnerUpdateAddress", type="object", required={"address_id"})
 */
class RaffleWinnerUpdateAddress
{

	/**
	 * @OA\Property(property="address_id", type="number")
	 */
	protected $address_id;
}

/**
 * @OA\Schema(schema="RaffleWinnerUpdateDeliveryInformation", type="object", required={"delivery_company", "track_id", "estimated_delivery_date", "delivery_voucher_file"})
 */
class RaffleWinnerUpdateDeliveryInformation
{

	/**
	 * @OA\Property(property="delivery_company", type="string")
	 */
	protected $delivery_company;

	/**
	 * @OA\Property(property="track_id", type="string")
	 */
	protected $track_id;

	/**
	 * @OA\Property(property="track_url", type="string")
	 */
	protected $track_url;


	/**
	 * @OA\Property(property="estimated_delivery_date", type="string", format="date-time")
	 */
	protected $estimated_delivery_date;

	/**
	 * @OA\Property(property="delivery_voucher_file", type="string", format="binary",example="file")
	 */
	protected $delivery_voucher_file;
}

/**
 * @OA\Schema(schema="RaffleWinnerUpdatePrizeStatus", type="object", required={"received_prize", "reception_date", "estimated_delivery_date"})
 */
class RaffleWinnerUpdatePrizeStatus
{

	/**
	 * @OA\Property(property="received_prize", type="integer")
	 */
	protected $received_prize;

	/**
	 * @OA\Property(property="reception_date", type="string", format="date-time")
	 */
	protected $reception_date;

	/**
	 * @OA\Property(property="evidence_file", type="string", format="binary",example="file")
	 */
	protected $evidence_file;
}
