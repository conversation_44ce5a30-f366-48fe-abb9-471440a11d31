<?php

/**
 *	@OA\Schema(
 *		schema="Paginated",
 *	 	type="object",
 *      @OA\Property(
 *   	   property="current_page",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="first_page_url",
 *         type="string",
 *      ),
 *      @OA\Property(
 *   	   property="from",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="last_page",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="last_page_url",
 *         type="string",
 *      ),
 *      @OA\Property(
 *   	   property="next_page_url",
 *         type="string",
 *      ),
 *      @OA\Property(
 *   	   property="path",
 *         type="string",
 *      ),
 *      @OA\Property(
 *   	   property="per_page",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="prev_page_url",
 *         type="string",
 *      ),
 *      @OA\Property(
 *   	   property="to",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="total",
 *         type="number",
 *      ),
 *  )
 */

/**
 *	@OA\Schema(
 *		schema="Catalog",
 *	 	type="object",
 *      @OA\Property(
 *   	   property="id",
 *         type="number",
 *      ),
 *      @OA\Property(
 *   	   property="name",
 *         type="string",
 *      ),
 *  )
 */

/**
 *	@OA\Schema(
 *		schema="Date",
 *	 	type="object",
 *      @OA\Property(
 *   	   property="date",
 *         type="string",
 *         type="datetime",
 *      )
 *  )
 */

/**
 * @OA\Schema(
 *     schema="CatalogDescription",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Catalog"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="description",
 *          type="string"
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(
 *     schema="Subcategory",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/CatalogDescription"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="category_id",
 *          type="number"
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(
 *     schema="CatalogBank",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Catalog"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="code",
 *          type="string"
 *       ),
 *    ),
 * }
 * )
 */