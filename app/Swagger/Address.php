<?php




/**
 * @OA\Schema(schema="AddressSave", type="object")
 */
class AddressSave
{
	/**
	 * @OA\Property(property="name", type="string")
	 */
	protected $name;

	/**
	 * @OA\Property(property="country", type="string")
	 */
	protected $country;

	/**
	 * @OA\Property(property="state", type="string")
	 */
	protected $state;

	/**
	 * @OA\Property(property="city", type="string")
	 */
	protected $city;


	/**
	 * @OA\Property(property="cp", type="string")
	 */
	protected $cp;

	/**
	 * @OA\Property(property="code_area", type="string")
	 */
	protected $code_area;

	/**
	 * @OA\Property(property="phone", type="string")
	 */
	protected $phone;

	/**
	 * @OA\Property(property="contact_name", type="string")
	 */
	protected $contact_name;
}


/**
 * @OA\Schema(
 *     schema="Address",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/AddressSave"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="id",
 *          type="number",
 *       ),
 *       @OA\Property(
 *          property="user_id",
 *          type="number",
 *       ),
 *       @OA\Property(
 *          property="created_at",
 *          type="string",
 * 			format="date-time"
 *       ),
 *       @OA\Property(
 *          property="updated_at",
 *          type="string",
 * 			format="date-time"
 *       ),
 *    ),
 * }
 * )
 */