<?php

/**
 * @OA\Schema(schema="RaffleReportSave", type="object", required={"raffle_id", "comments"})
 */
class RaffleReportSave
{
	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="comments", type="string")
	 */
	protected $comments;
}

/**
 * @OA\Schema(schema="RaffleReportEdit", type="object", required={"raffle_id", "comments"})
 */
class RaffleReportEdit
{
	/**
	 * @OA\Property(property="status", type="string")
	 */
	protected $status;

	/**
	 * @OA\Property(property="admin_comments", type="string")
	 */
	protected $admin_comments;
}

/**
 * @OA\Schema(schema="RaffleReport", type="object")
 */
class RaffleReport
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="user_id", type="number")
	 */
	protected $user_id;

	/**
	 * @OA\Property(property="admin_id", type="number")
	 */
	protected $admin_id;

	/**
	 * @OA\Property(property="status", type="string")
	 */
	protected $status;

	/**
	 * @OA\Property(property="comments", type="string")
	 */
	protected $comments;

	/**
	 * @OA\Property(property="admin_comments", type="string")
	 */
	protected $admin_comments;

	/**
	 * @OA\Property(property="reviewed_date", type="string", format="date-time")
	 */
	protected $reviewed_date;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}