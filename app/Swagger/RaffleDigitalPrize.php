<?php

/**
 * @OA\Schema(schema="RaffleDigitalPrize", type="object")
 */
class RaffleDigitalPrize
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="raffle_id", type="number")
	 */
	protected $raffle_id;

	/**
	 * @OA\Property(property="instructions", type="string")
	 */
	protected $instructions;

	/**
	 * @OA\Property(property="redeem_code", type="string")
	 */
	protected $redeem_code;

	/**
	 * @OA\Property(property="external_link", type="string")
	 */
	protected $external_link;

	/**
	 * @OA\Property(property="url_instructions", type="string")
	 */
	protected $url_instructions;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}

/**
 * @OA\Schema(schema="RaffleDigitalPrizeUpdateInstructions", type="object", required={"instructions"})
 */
class RaffleDigitalPrizeUpdateInstructions
{
	/**
	 * @OA\Property(property="instructions", type="string")
	 */
	protected $instructions;

	/**
	 * @OA\Property(property="redeem_code", type="string")
	 */
	protected $redeem_code;

	/**
	 * @OA\Property(property="external_link", type="string")
	 */
	protected $external_link;

	/**
	 * @OA\Property(property="instructions_file", type="string", format="binary",example="file")
	 */
	protected $instructions_file;
}
