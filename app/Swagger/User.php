<?php



/**
 * @OA\Schema(schema="User", type="object")
 */
class User
{
    /**
     * @OA\Property(property="id", type="number")
     */
    protected $id;

    /**
     * @OA\Property(property="name", type="string")
     */
    protected $name;

    /**
     * @OA\Property(property="last_name", type="string")
     */
    protected $last_name;

    /**
     * @OA\Property(property="nick_name", type="string")
     */
    protected $nick_name;

    /**
     * @OA\Property(property="email", type="string")
     */
    protected $email;

    /**
     * @OA\Property(property="url_photo", type="string")
     */
    protected $url_photo;

    /**
     * @OA\Property(property="created_at", type="string",example="2021-04-28 12:56:29")
     */
    protected $created_at;

    /**
     * @OA\Property(property="updated_at", type="string",example="2021-04-28 12:56:29")
     */
    protected $updated_at;

    /**
     * @OA\Property(property="ticket_locked_finish", type="string",example="2021-04-28 12:56:29")
     */
    protected $ticket_locked_finish;

    /**
     * @OA\Property(property="role", type="string")
     */
    protected $role;
}

/**
 * @OA\Schema(
 *     schema="UserPermissions",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/User"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="role",
 *          type="string",
 *       ),
 *       @OA\Property(
 *          property="permission",
 *          type="array",
 *          @OA\Items(type="string")
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(schema="UserPermissionsToken", type="object")
 */
class UserPermissionsToken
{
    /**
     *       @OA\Property(
     *          property="user",
     *          type="object",
     *          ref="#/components/schemas/UserPermissions"
     *       )
     */

    /**
     *       @OA\Property(
     *          property="access_token",
     *          type="string"
     *       )
     */
}


