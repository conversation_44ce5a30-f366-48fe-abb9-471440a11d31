<?php

/**
 * @OA\Schema(schema="ApplicationSave", type="object", required={"id_front_file", "bank_information_file"})
 */
class ApplicationSave
{
	/**
	 * @OA\Property(property="id_front_file", type="string", format="binary",example="file")
	 */
	protected $id_front_file;

	/**
	 * @OA\Property(property="id_back_file", type="string", format="binary",example="file")
	 */
	protected $id_back_file;

	/**
	 * @OA\Property(property="bank_information_file", type="string")
	 */
	protected $bank_information_file;

	/**
	 * @OA\Property(property="rfc", type="string", format="binary",example="file")
	 */
	protected $rfc;

	/**
	 * @OA\Property(property="url_rfc_documentation", type="string", format="binary",example="file")
	 */
	protected $url_rfc_documentation;
}

/**
 * @OA\Schema(schema="ApplicationEdit", type="object", required={"approved", "rejection_reason"})
 */
class ApplicationEdit
{
	/**
	 * @OA\Property(property="approved", type="boolean")
	 */
	protected $approved;

	/**
	 * @OA\Property(property="rejection_reason", type="string")
	 */
	protected $rejection_reason;
}

/**
 * @OA\Schema(schema="ApplicationEditRFC", type="object", required={"rfc", "url_rfc_documentation"})
 */
class ApplicationEditRFC
{
	/**
	 * @OA\Property(property="rfc", type="string")
	 */
	protected $rfc;

	/**
	 * @OA\Property(property="url_rfc_documentation", type="string", format="binary",example="file")
	 */
	protected $url_rfc_documentation;
}

/**
 * @OA\Schema(schema="Application", type="object")
 */
class Application
{
	/**
	 * @OA\Property(property="id", type="number")
	 */
	protected $id;

	/**
	 * @OA\Property(property="user_id", type="number")
	 */
	protected $user_id;

	/**
	 * @OA\Property(property="admin_id", type="number")
	 */
	protected $admin_id;

	/**
	 * @OA\Property(property="rfc", type="string")
	 */
	protected $rfc;

	/**
	 * @OA\Property(property="url_id_front", type="string")
	 */
	protected $url_id_front;

	/**
	 * @OA\Property(property="url_id_back", type="string")
	 */
	protected $url_id_back;

	/**
	 * @OA\Property(property="url_bank_information", type="string")
	 */
	protected $url_bank_information;

	/**
	 * @OA\Property(property="approval", type="boolean")
	 */
	protected $approval;

	/**
	 * @OA\Property(property="approval_date", type="string", format="date-time")
	 */
	protected $approval_date;

	/**
	 * @OA\Property(property="rejection_reason", type="string")
	 */
	protected $rejection_reason;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}