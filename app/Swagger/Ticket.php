<?php



/**
 * @OA\Schema(schema="Ticket", type="object")
 */
class Ticket
{

    /**
     * @OA\Property(property="id", type="number")
     */
    protected $id;

    /**
     * @OA\Property(property="number", type="number")
     */
    protected $number;

    /**
     * @OA\Property(property="locked", type="boolean")
     */
    protected $locked;

    /**
     * @OA\Property(property="locked_finish", type="string", example="2021-04-28 12:56:29")
     */
    protected $locked_finish;

    /**
     * @OA\Property(property="buy_date", type="string", example="2021-04-28 12:56:29")
     */
    protected $buy_date;

    /**
     * @OA\Property(property="fast_purchase", type="boolean")
     */
    protected $fast_purchase;

    /**
     * @OA\Property(property="status", type="string", example=" 'Free' | 'Locked' | 'Sold' ")
     */
    protected $status;

    /**
     * @OA\Property(property="payment_type", type="string", example=" 'Conekta'| 'Rafiki' | 'Points'  ")
     */
    protected $payment_type;

    /**
     * @OA\Property(property="raffle_id", type="number")
     */
    protected $raffle_id;

    /**
     * @OA\Property(property="buyer_id", type="number")
     */
    protected $buyer_id;

}



