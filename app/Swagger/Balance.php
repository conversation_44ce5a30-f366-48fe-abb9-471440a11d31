<?php

/**
 * @OA\Schema(schema="BalanceSave", type="object")
 */
class BalanceSave
{
	/**
	 * @OA\Property(property="amount", type="number")
	 */
	protected $amount;

	/**
	 * @OA\Property(property="points", type="number")
	 */
	protected $points;

	/**
	 * @OA\Property(property="commission", type="number")
	 */
	protected $commission;

	/**
	 * @OA\Property(property="user_id", type="number")
	 */
	protected $user_id;

	/**
	 * @OA\Property(property="card_id", type="number")
	 */
	protected $card_id;

	/**
	 * @OA\Property(property="created_at", type="string", format="date-time")
	 */
	protected $created_at;

	/**
	 * @OA\Property(property="updated_at", type="string", format="date-time")
	 */
	protected $updated_at;
}


/**
 * @OA\Schema(
 *     schema="Balance",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/BalanceSave"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="id",
 *          type="number",
 *       ),
 *    ),
 * }
 * )
 */


/**
 * @OA\Schema(schema="Wallet", type="object")
 */
class Wallet
{
	/**
	 * @OA\Property(property="total", type="number")
	 */
	protected $total;

	/**
	 *       @OA\Property(
	 *          property="deposits",
	 *          type="array",
	 *          @OA\Items(ref="#/components/schemas/Balance")
	 *       ),
	 */
	protected $deposits;

	/**
	 *       @OA\Property(
	 *          property="payments",
	 *          type="array",
	 *          @OA\Items(ref="#/components/schemas/PaymentOrder")
	 *       ),
	 */
	protected $payments;
}
