<?php



/**
 * @OA\Schema(schema="RaffleSave", type="object",required={"name","cat_state_id","start","finish","prize_value","num_tickets","ticket_price", "active", "product_type", "subcategory_id"})
 */
class RaffleSave
{

    /**
     * @OA\Property(property="name", type="string")
     */
    protected $name;

    /**
     * @OA\Property(property="brand", type="string")
     */
    protected $brand;

    /**
     * @OA\Property(property="model", type="string")
     */
    protected $model;

    /**
     * @OA\Property(property="description", type="string")
     */
    protected $description;

    /**
     * @OA\Property(property="cat_state_id", type="number")
     */
    protected $cat_state_id;

    /**
     * @OA\Property(property="city", type="string")
     */
    protected $city;

    /**
     * @OA\Property(property="start", type="string",example="2021-04-28 12:56:29")
     */
    protected $start;

    /**
     * @OA\Property(property="finish", type="string",example="2021-04-28 12:56:29")
     */
    protected $finish;

    /**
     * @OA\Property(property="prize_value", type="number")
     */
    protected $prize_value;


    /**
     * @OA\Property(property="num_tickets", type="number")
     */
    protected $num_tickets;

    /**
     * @OA\Property(property="selled_tickets", type="number")
     */
    protected $selled_tickets;

    /**
     * @OA\Property(property="ticket_price", type="number")
     */
    protected $ticket_price;

    /**
     * @OA\Property(property="suggested_value", type="number")
     */
    protected $suggested_value;

    /**
     * @OA\Property(property="active", type="boolean")
     */
    protected $active;

    /**
     * @OA\Property(property="canceled", type="boolean")
     */
    protected $canceled;


    /**
     * @OA\Property(property="cancel_comments", type="string")
     */
    protected $cancel_comments;

    /**
     * @OA\Property(property="product_type", type="string", example=" 'Physical' | 'Digital' | 'Service' ")
     */
    protected $product_type;

    /**
     * @OA\Property(property="prize_type", type="string", example=" 'New' | 'Used' ")
     */
    protected $prize_type;

    /**
     * @OA\Property(property="subcategory_id", type="number")
     */
    protected $subcategory_id;

    /**
    *
    * @OA\Property(
    *   property="images",
    *   type="array",
    *   @OA\Items(ref="#/components/schemas/RafflePhotoSave")
    *  )
    */

    /**
     *       @OA\Property(
     *          property="tops",
     *          type="array",
     *          @OA\Items(ref="#/components/schemas/Tops")
     *       ),
     */

    /**
     *       @OA\Property(
     *          property="report",
     *          type="object",
     *          ref="#/components/schemas/RaffleReport"
     *       ),
     */
}

/**
 * @OA\Schema(
 *     schema="Raffle",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/RaffleSave"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="id",
 *          type="number",
 *       ),
 *    ),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="min_percentage",
 *          type="number",
 *       ),
 *    ),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="status",
 *          type="string",
 *          example=" 'pending' | 'canceled' | 'raffled' "
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(
 *     schema="RaffleWinnerDetail",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Raffle"),
 *     @OA\Schema(
 *       @OA\Property(
 *          property="raffle_winner",
 *          type="object",
 *          ref="#/components/schemas/RaffleWinner"
 *      ),
 *    ),
 *     @OA\Schema(
 *       @OA\Property(
 *          property="digital_prize",
 *          type="object",
 *          ref="#/components/schemas/RaffleDigitalPrize"
 *      ),
 *    ),
 * }
 * )
 */


/**
 * @OA\Schema(
 *     schema="RafflePaginated",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Paginated"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="data",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/Raffle")
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(schema="RafflePhotoSave", type="object",required={"url_image","default"})
 */
class RafflePhotoSave
{

    /**
     * @OA\Property(property="url_image", type="string")
     */
    protected $url_image;

    /**
     * @OA\Property(property="default", type="boolean")
     */
    protected $default;

}


/**
 * @OA\Schema(
 *     schema="RaffleTickets",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Raffle"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="tickets",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/RaffleTicket")
 *       ),
 *       @OA\Property(
 *          property="tops",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/Tops")
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(schema="Tops", type="object")
 */
class Tops
{

    /**
     * @OA\Property(property="total", type="number")
     */
    protected $total;

}


/**
 * @OA\Schema(schema="RaffleTicket", type="object")
 */
class RaffleTicket
{

    /**
     * @OA\Property(property="number", type="number")
     */
    protected $number;

    /**
     * @OA\Property(property="locked", type="boolean")
     */
    protected $locked;

    /**
     * @OA\Property(property="locked_finish", type="string", example="2021-04-28 12:56:29")
     */
    protected $locked_finish;

    /**
     * @OA\Property(property="status", type="string", example=" 'Free' | 'Locked' | 'Sold' ")
     */
    protected $status;

    /**
     * @OA\Property(property="raffle_id", type="number")
     */
    protected $raffle_id;

    /**
     * @OA\Property(property="buyer_id", type="number")
     */
    protected $buyer_id;

}


/**
 * @OA\Schema(
 *     schema="RaffleRecordPaginated",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Paginated"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="data",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/RaffleRecord")
 *       ),
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(
 *     schema="RaffleRecord",
 * allOf={
 *    @OA\Schema(ref="#/components/schemas/Raffle"),
 *    @OA\Schema(
 *       @OA\Property(
 *          property="tickets",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/RaffleRecordTicket")
 *       ),
 *       @OA\Property(
 *          property="images",
 *          type="array",
 *          @OA\Items(ref="#/components/schemas/RafflePhotoSave")
 *       )
 *    ),
 * }
 * )
 */

/**
 * @OA\Schema(schema="RaffleRecordTicket", type="object")
 */
class RaffleRecordTicket
{

    /**
     * @OA\Property(property="number", type="number")
     */
    protected $number;

    /**
     * @OA\Property(property="status", type="string", example=" 'Free' | 'Locked' | 'Buyed' ")
     */
    protected $status;

    /**
     * @OA\Property(property="raffle_id", type="number")
     */
    protected $raffle_id;

    /**
     * @OA\Property(property="buyer_id", type="number")
     */
    protected $buyer_id;

    /**
     * @OA\Property(property="fast_purchase", type="boolean")
     */
    protected $fast_purchase;

    /**
     * @OA\Property(property="payment_type", type="string", example=" 'Conekta'| 'Rafiki' | 'Points'  ")
     */
    protected $payment_type;

    /**
     * @OA\Property(property="buy_date", type="string", example="2021-04-28 12:56:29")
     */
    protected $buy_date;


}

