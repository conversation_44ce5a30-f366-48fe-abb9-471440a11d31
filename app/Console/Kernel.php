<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Console\Commands\UnlockTickets;
use App\Console\Commands\RaffleWinners;
use App\Console\Commands\SendNotifications;
use App\Console\Commands\RaffleEndsTwoDays;

class <PERSON>el extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        UnlockTickets::class,
        RaffleWinners::class,
        SendNotifications::class,
        RaffleEndsTwoDays::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('rafiki:unlock-tickets')
                 ->everyMinute();

        $schedule->command('rafiki:winners')
                 ->everyMinute();

        $schedule->command('rafiki:send-notifications-sold-out-tickets')       
                 ->dailyAt('22:00');   

        $schedule->command('rafiki:raffleEndsTwoDays')       
                 ->dailyAt('08:00');        
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
