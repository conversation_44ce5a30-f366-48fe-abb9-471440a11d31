<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Model\Raffle;
use App\Model\RaffleDraw;
use App\Model\RaffleWinner;
use App\Model\Ticket;
use App\Model\Notification;
use App\Helpers\AppHelper;
use App\User;

use App\Http\Controllers\RaffleController;

use Carbon\Carbon;

use Illuminate\Support\Facades\Mail;
use App\Mail\Winner;
use App\Mail\NotDrawRaffle;
use App\Mail\RaffleDraw as DrawMail;

class RaffleWinners extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rafiki:winners';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Select raffle winners';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $raffles = Raffle::whereDoesntHave('draw')
            ->where('finish','<=',Carbon::now())
            ->where('canceled',false)
            ->where('active', true)
            ->get();

        $this->info('Raffles:' . $raffles->count());

        $controller = new RaffleController;

        foreach ($raffles as $raffle) {
            $total = $raffle->sold_tickets * $raffle->ticket_price;
            //TODO: cambiar para la prueba
            $expected = $raffle->prize_value * 2;
            //$expected = 1;  // minimo de boletos vendidos para poder realizar la rifa

            if($total >= $expected){
                $winner = $controller->draw($raffle);
            }else{
                $user = User::find($raffle->user_id);

                Mail::to($user->email)
                    ->send(
                        new NotDrawRaffle($user,$raffle)
                    );

                $raffle->active = 0;
                $raffle->canceled = 1;
                $raffle->cancel_comments = 'La rifa no alcanzó el mínimo para ejecutarse';
                $raffle->save();

                //$raffle->cancelNotifications($raffle);
            }

            /*
            $percent = ($raffle->sold_tickets / $raffle->tickets->count()) * 100;

            if($percent >= 80){
                $this->info('---> Hacer sorteo');
                $winner = $controller->draw($raffle);
            }else{
                $this->info('---> Enviar mail al rifante');
            }*/

            //$this->info('Raffle winner:' . $raffle->winner->number);
        }
    }
}
