<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Model\Raffle;
use App\Model\Ticket;
use App\User;
use App\Model\Notification;
use App\Helpers\AppHelper;
use Illuminate\Support\Facades\Mail;

use Carbon\Carbon;

use App\Mail\SoldOutTickets;

class SendNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rafiki:send-notifications-sold-out-tickets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notification of sold out tickets';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $raffles = Raffle::where('finish','<=',Carbon::now())
            ->where('canceled',false)
            ->where('active',true)
            ->where('status_notification','pending')
            ->get();

            $this->info('Raffles:' . $raffles->count());

            foreach ($raffles as $raffle) {

                $soldTickets = Ticket::where('raffle_id', $raffle->id)->where('status', 'Sold')->count();

                $totalTickets = Ticket::where('raffle_id', $raffle->id)->count();

                if ($soldTickets == $totalTickets) {
                    $this->info('RafflesID:' . $raffle->id);
                    $raffle->status_notification='in_progress';
                    $raffle->save();

                    $user = User::find($raffle->user_id);

            
                    Mail::to($user->email)
                    ->send(
                        new SoldOutTickets($user,$raffle)
                    );

                    $data = [
                        'click_action' => "FLUTTER_NOTIFICATION_CLICK",
                        'raffle_id' => $raffle->id,
                        'user_id' => $user->id,
                    ];

                    Notification::create([
                        'title'   => 'Boletos agotados de la rifa '.$raffle->name ,
                        'message' => 'Se han agotado los boletos de esta rifa, espera la fecha de ejecución.',
                        'data'    => $raffle->toJson(),
                        'user_id' => $user->id,
                        'cat_notification_type_id' => 12
                    ]);
        
                    try{
                        AppHelper::sendPushNotification(
                            $user->id,
                            'Boletos agotados de la rifa '.$raffle->name,
                            'Se han agotado los boletos de esta rifa, espera la fecha de ejecución.',
                            $data
                        );
                    }catch(\Exception $e){
                        Log::debug($e->getMessage());
                    }

                    sleep(1);

                    $users = Ticket::select('buyer_id')
                                    ->distinct()
                                    ->where('status','sold')
                                    ->where('raffle_id',$raffle->id)
                                    ->get()
                                    ->pluck('buyer_id');

                    foreach ($users as $idUserTicket) {
                        $userTicket = User::find($idUserTicket);

                        Mail::to($userTicket->email)
                        ->send(
                            new SoldOutTickets($userTicket,$raffle)
                        );

                        $data = [
                            'click_action' => "FLUTTER_NOTIFICATION_CLICK",
                            'raffle_id' => $raffle->id,
                            'user_id' => $idUserTicket,
                        ];

                        Notification::create([
                            'title'   => 'Boletos agotados de la rifa '.$raffle->name,
                            'message' => 'Se han agotado los boletos de esta rifa, espera la fecha de ejecución para saber si eres ganador o no ¡Suerte!.',
                            'data'    => $raffle->toJson(),
                            'user_id' => $idUserTicket,
                            'cat_notification_type_id' => 13
                        ]);
            
                        try{
                            AppHelper::sendPushNotification(
                                $idUserTicket,
                                'Boletos agotados de la rifa '.$raffle->name,
                                'Se han agotado los boletos de esta rifa, espera la fecha de ejecución para saber si eres ganador o no ¡Suerte!.',
                                $data
                            );
                        }catch(\Exception $e){
                            Log::debug($e->getMessage());
                        }
                    }

                    $raffle->status_notification='complete';
                    $raffle->save();
                }
            }
    }
}
