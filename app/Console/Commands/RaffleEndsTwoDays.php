<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Model\Raffle;
use App\Model\Ticket;
use App\User;
use App\Model\Notification;
use App\Helpers\AppHelper;
use Illuminate\Support\Facades\Mail;

use Carbon\Carbon;

use App\Mail\SendRaffleEndsTwoDays;

class RaffleEndsTwoDays extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rafiki:raffleEndsTwoDays';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notification when there are two days left for the raffle to end';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $raffles = Raffle::where('canceled',false)
            ->where('active',true)
            ->get();
    
        $this->info('Raffles:' . $raffles->count());

        foreach ($raffles as $raffle) {
            $finish = Carbon::create($raffle->finish);
            $finish->hour = 0;
            $finish->minute = 0;
            $finish->second = 0;
            //$this->info('Fecha fin de la rifa:' . $finish);

            $nextDate = $finish->subDay(2);
            $nextDate->hour = 0;
            $nextDate->minute = 0;
            $nextDate->second = 0;
            //$this->info('Fecha proxima a terminar:' . $nextDate);

            $nowDate =  Carbon::now();
            $nowDate->hour = 0;
            $nowDate->minute = 0;
            $nowDate->second = 0;            

            if($nowDate == $nextDate) {
                $user = User::find($raffle->user_id);                
                
                Mail::to($user->email)
                ->send(
                    new SendRaffleEndsTwoDays($user,$raffle)
                );

                $data = [
                    'click_action' => "FLUTTER_NOTIFICATION_CLICK",
                    'raffle_id' => $raffle->id,
                    'user_id' => $user->id,
                ];

                Notification::create([
                    'title'   => 'La rifa '.$raffle->name.', esta próxima a terminar.',
                    'message' => 'Rifa próxima a terminar.',
                    'data'    => $raffle->toJson(),
                    'user_id' => $user->id,
                    'cat_notification_type_id' => 14
                ]);

                try{
                    AppHelper::sendPushNotification(
                        $user->id,
                        'La rifa '.$raffle->name.', esta próxima a terminar.',
                        'Rifa próxima a terminar.',
                        $data
                    );
                }catch(\Exception $e){
                    Log::debug($e->getMessage());
                }

                sleep(1);

                $users = Ticket::select('buyer_id')
                                ->distinct()
                                ->where('status','sold')
                                ->where('raffle_id',$raffle->id)
                                ->get()
                                ->pluck('buyer_id');
                
                foreach ($users as $idUserTicket) {
                    $userTicket = User::find($idUserTicket);

                    Mail::to($user->email)
                    ->send(
                        new SendRaffleEndsTwoDays($user,$raffle)
                    );

                    $data = [
                        'click_action' => "FLUTTER_NOTIFICATION_CLICK",
                        'raffle_id' => $raffle->id,
                        'user_id' => $idUserTicket,
                    ];

                    Notification::create([
                        'title'   => 'La rifa '.$raffle->name.', esta próxima a terminar.',
                        'message' => 'Rifa próxima a terminar.',
                        'data'    => $raffle->toJson(),
                        'user_id' => $idUserTicket,
                        'cat_notification_type_id' => 15
                    ]);

                    try{
                        AppHelper::sendPushNotification(
                            $idUserTicket,
                            'La rifa '.$raffle->name.', esta próxima a terminar.',
                            'Rifa próxima a terminar.',
                            $data
                        );
                    }catch(\Exception $e){
                        Log::debug($e->getMessage());
                    }
                }
            }
        }
    }
}
