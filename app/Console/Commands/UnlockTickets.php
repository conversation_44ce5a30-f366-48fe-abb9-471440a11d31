<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\User;
use App\Model\Ticket;
use Carbon\Carbon;

class UnlockTickets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rafiki:unlock-tickets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Unlock tickets with completed time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $tickets = Ticket::where('locked',1)
                    ->where('locked_finish','<=',Carbon::now())
                    ->update(
                        [
                            "locked" => false,
                            "locked_finish" => null,
                            "buyer_id" => null,
                            "status" => "Free",
                        ]);

        $this->info('Tickets updated:' . $tickets);

        $users = User::where('ticket_locked_finish','<=',Carbon::now())
            ->update(
                [
                    "ticket_locked_finish" => null
                ]
            );

        $this->info('User updated:' . $users);
    }
}
