<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Auth\Notifications\ResetPassword;

use Illuminate\Support\Facades\Log;

class CustomResetPassword extends ResetPassword
{
    public $email;

    public function __construct($token, $email)
    {
        $this->token = $token;
        $this->email = $email;
    }

    public function toMail($notifiable)
    {
        $fhost = config('app.front_host');
        
        $url = url($fhost.'reset-password?token='.$this->token.'&email='.urlencode($this->email));
        
        return (new MailMessage)
                ->subject('Rafiki - Recuperar contraseña')
                ->view('emails.resetPassword',[
                    'url' => $url
                ]);
    }
}
