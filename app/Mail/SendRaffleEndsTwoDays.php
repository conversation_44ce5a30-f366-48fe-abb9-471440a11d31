<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendRaffleEndsTwoDays extends Mailable
{
    use Queueable, SerializesModels;

    protected $raffle;
    protected $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $raffle)
    {
        $this->user = $user;
        $this->raffle = $raffle;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $name = $this->raffle->name;
        return $this->view('emails.sendRaffleEndsTwoDays')
                    ->subject("Rifa próxima a terminar- Ra<PERSON>ki")
                    ->with(
                        [
                            'user' => $this->user,
                            'raffle' => $this->raffle,
                        ]
                    );
    }
}
