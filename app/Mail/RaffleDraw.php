<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

use App\Model\Application;

class RaffleDraw extends Mailable
{
    use Queueable, SerializesModels;

    protected $raffle;
    protected $user;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $raffle)
    {
        //
        $this->user = $user;
        $this->raffle = $raffle;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {   
        $amount = $this->raffle->sold_tickets * $this->raffle->ticket_price;
        $fee = $amount * 0.2;
        $fee_tax = $fee * 0.16;

        $application = Application::where('user_id',$this->raffle->user_id)
            ->where('approval',true)
            ->first();

        $isr_p = 20;
        if($application != null && ($application->rfc != null && $application->rfc != '')){
            $isr_p = 1;
        }

        $isr = $amount * ( $isr_p / 100); 

        $deposit = $amount - $fee - $fee_tax - $isr;


        $name = $this->raffle->name;
        return $this->view('emails.raffleDraw')
                    ->subject("Sorteo ejecutado $name - Rafiki")
                    ->with(
                        [
                            'user' => $this->user,
                            'raffle' => $this->raffle,
                            'amount' => $amount,
                            'fee' => $fee,
                            'fee_tax' => $fee_tax,
                            'isr_p' => $isr_p,
                            'isr' => $isr,
                            'deposit' => $deposit,
                        ]
                    );
    }
}
