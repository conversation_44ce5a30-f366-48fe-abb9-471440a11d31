<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CancelRaffle extends Mailable
{
    use Queueable, SerializesModels;

    protected $raffle;
    protected $report;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($raffle)
    {
        //
        $this->raffle = $raffle;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $name = $this->raffle->name;
        return $this->view('emails.cancelRaffle')
                    ->subject("Rifa $name cancelada - Ra<PERSON>ki")
                    ->with(
                        [
                            'raffle' => $this->raffle,
                        ]
                    );
    }
}
