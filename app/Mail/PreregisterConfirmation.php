<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Model\Preregister;
use App\Model\Event;

class PreregisterConfirmation extends Mailable
{
    use Queueable, SerializesModels;

    protected $eventId;
    protected $registerId;
    protected $event;
    protected $register;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($event,$register)
    {
        $this->eventId = $event;
        $this->registerId = $register;

        $this->event = Event::findOrFail($event);
        $this->register = Preregister::findOrFail($register);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.preregisterConfirmation')
                    ->with(
                        [
                            'event' => $this->event,
                            'register' => $this->register,
                        ]
                    );
    }
}
