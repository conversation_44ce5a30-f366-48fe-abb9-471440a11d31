<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AddApplication extends Mailable
{
    use Queueable, SerializesModels;

    protected $user;
    protected $application;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $application)
    {
        //
        $this->user = $user;
        $this->application = $application;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        
        return $this->view('emails.addApplication')
                    ->subject("Rafiki - Solicitud para validar documentación")
                    ->with(
                        [
                            'user' => $this->user,
                            'application_url' => config('app.front_host').'admin/application/edit/'.$this->application->id,
                        ]
                    );
    }
}
