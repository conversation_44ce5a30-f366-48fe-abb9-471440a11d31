<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PaymentOrder extends Mailable
{
    use Queueable, SerializesModels;

    protected $user;
    protected $order;
    protected $raffle;
    protected $tickets;


    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $order, $raffle, $tickets)
    {
        $this->user = $user;
        $this->order = $order;
        $this->raffle = $raffle;
        $this->tickets = $tickets;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.paymentOrder')
                    ->subject('Compra exitosa - Rafiki')
                    ->with(
                        [
                            'user' => $this->user,
                            'order' => $this->order,
                            'raffle' => $this->raffle,
                            'tickets' => $this->tickets,
                        ]
                    );
    }
}
