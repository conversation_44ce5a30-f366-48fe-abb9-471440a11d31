<?php

namespace App\Helpers;

use App\Model\Device;
use <PERSON><PERSON><PERSON>M\Message\OptionsBuilder;
use <PERSON><PERSON><PERSON>M\Message\PayloadDataBuilder;
use <PERSON>velFCM\Message\PayloadNotificationBuilder;
use FCM;

class AppHelper
{
	public static function sendPushNotification($user, $title, $message, $data)
	{
		$optionBuilder = new OptionsBuilder();
		$optionBuilder->setTimeToLive(60 * 20);

		$notificationBuilder = new PayloadNotificationBuilder($title);
		$notificationBuilder->setBody($message)
			->setSound('default');

		$dataBuilder = new PayloadDataBuilder();
		$dataBuilder->addData($data);

		$option = $optionBuilder->build();
		$notification = $notificationBuilder->build();
		$data = $dataBuilder->build();

		$devices = Device::where(
			[
				"user_id" => $user,
				"active" => 1,
			]
		)
			->get()
			->pluck('token')
			->toArray();

		if (count($devices) == 0) {
			return [];
		}

		try {
			$downstreamResponse = FCM::sendTo($devices, $option, $notification, $data);
		} catch (\Exception $e) {
			return [];
			\Log::debug($e->getMessage());
		}

		$response = [];
		$response['success'] = $downstreamResponse->numberSuccess();
		$response['failure'] = $downstreamResponse->numberFailure();
		$response['modification'] = $downstreamResponse->numberModification();

		$response['delete'] = $downstreamResponse->tokensToDelete();
		foreach ($response['delete'] as $delete) {
			$device = Device::where(
				[
					"token" => $delete,
					"active" => 1
				]
			)
				->first();

			if ($device != null) {
				$device->active = 0;
				$device->save();
			}
		}

		$response['modify'] = $downstreamResponse->tokensToModify();

		try {

			foreach ($response['modify'] as $key => $value) {
				$device = Device::where(
					[
						"token" => $key,
					]
				)
					->first();

				$device->token = $value;
				$device->save();
			}
		} catch (\Exception $e) {
			\Log::debug($e->getMessage());
		}

		$response['retry'] = $downstreamResponse->tokensToRetry();

		$response['error'] = $downstreamResponse->tokensWithError();

		return $response;
	}

	public static function instance()
	{
		return new AppHelper();
	}
}
