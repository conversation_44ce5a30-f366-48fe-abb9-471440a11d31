<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EditApplication extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'approval'         => 'required|in:1,0',
			'rejection_reason' => 'required_if:approval,0'
        ];
    }
}
