<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'email|required|unique:users|max:255',
            'name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'nickname' => 'required|string|unique:users|max:255',
            'password' => 'required_without_all:facebook_id,apple_id|string|max:255',
            'facebook_id' => 'required_without_all:password,apple_id|string|max:255',
            'apple_id' => 'required_without_all:password,facebook_id|string|max:255',
        ];
    }
}