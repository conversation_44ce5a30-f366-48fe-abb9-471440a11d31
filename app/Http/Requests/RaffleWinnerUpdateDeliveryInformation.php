<?php

namespace App\Http\Requests;

use App\Model\RaffleWinner;
use Illuminate\Foundation\Http\FormRequest;

class RaffleWinnerUpdateDeliveryInformation extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		$raffleWinnerId = $this->route('raffleWinner');

		$raffleWinner = RaffleWinner::find($raffleWinnerId)->first();

		if ($raffleWinner && $raffleWinner->raffle->user_id === request()->user()->id) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'delivery_company'        => 'required',
			'track_id'                => 'required',
			'estimated_delivery_date' => 'required',
			'track_url' => 'required|url',
			'delivery_voucher_file'   => 'required|file'
		];
	}
}
