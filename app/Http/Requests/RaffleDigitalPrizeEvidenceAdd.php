<?php

namespace App\Http\Requests;

use App\Model\RaffleWinner;
use Illuminate\Foundation\Http\FormRequest;

class RaffleDigitalPrizeEvidenceAdd extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		$raffle = $this->route('raffle');

		$raffleWinnerExists = RaffleWinner::where('raffle_id', $raffle->id)
										  ->where('winner_id', request()->user()->id)
										  ->exists();

		return $raffleWinnerExists;
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'type'      => 'required|in:external,instructions,file_instructions,redeem_code',
		];
	}
}
