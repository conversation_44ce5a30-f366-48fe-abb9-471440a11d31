<?php

namespace App\Http\Requests;

use App\Model\RaffleWinner;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RaffleWinnerUpdateAddress extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		$raffleWinnerId = $this->route('raffleWinner');

		$raffleWinner = RaffleWinner::find($raffleWinnerId)->first();

		if ($raffleWinner && $raffleWinner->winner_id === request()->user()->id) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'address_id' => [
				'required',
				Rule::exists('addresses', 'id')->where(function ($query) {
					$query->where('user_id', request()->user()->id);
				}),
			],
		];
	}
}
