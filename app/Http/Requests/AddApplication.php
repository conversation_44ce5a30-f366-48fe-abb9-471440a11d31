<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddApplication extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id_front_file'         => 'required|file',
			'id_back_file'          => 'file',
			'bank_information_file' => 'required|file',
            'rfc' => 'required_with:url_rfc_documentation|string|max:14|min:13',
            'url_rfc_documentation' => 'required_with:rfc|file'
        ];
    }
}
