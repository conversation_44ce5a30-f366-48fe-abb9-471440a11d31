<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EditApplicationRFC extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'rfc' => 'required_with:url_rfc_documentation|string|max:14|min:13',
            'url_rfc_documentation' => 'required_with:rfc|file'
        ];
    }
}
