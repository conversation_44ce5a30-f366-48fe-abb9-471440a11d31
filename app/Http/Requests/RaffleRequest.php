<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;

class RaffleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'brand' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'cat_state_id' => 'required|exists:cat_states,id',
            'start' => 'required|date',
            'finish' => 'required|date',
            'prize_value' => 'required|numeric',
            'num_tickets' => 'required|numeric',
            'ticket_price' => 'required|integer',
            'active' => 'required|boolean',
            'product_type' => 'required|string',
            'subcategory_id' => 'required|exists:subcategories,id',
            'images' => 'nullable|array',
            'images.*.url_image' => 'required|string',
            'images.*.default' => 'required|boolean',
        ];
    }
}
