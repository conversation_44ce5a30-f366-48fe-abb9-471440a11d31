<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RaffleDigitalPrizeAdd extends FormRequest
{
	/**
	 * Determine if the user is authorized to make this request.
	 *
	 * @return bool
	 */
	public function authorize()
	{
		$raffle = $this->route('raffle');

		if ($raffle && $raffle->user_id === request()->user()->id) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Get the validation rules that apply to the request.
	 *
	 * @return array
	 */
	public function rules()
	{
		return [
			'instructions'      => 'required',
			'instructions_file' => 'file'
		];
	}
}
