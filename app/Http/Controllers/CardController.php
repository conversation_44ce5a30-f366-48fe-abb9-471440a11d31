<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Card;
use App\Model\Raffle;
use Illuminate\Validation\Rule;

use Illuminate\Support\Facades\Log;

class CardController extends Controller
{
    /**
     * @OA\Get(
     *      path="/card",
     *      tags={"Tarjetas"},
     *      summary="Listado de tarjetas",
     *      description="Listado de tarjetas",
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Card")
     *          )
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     */
    public function index()
    {
        $user = auth()->user();
        $user->load('cards');

        return $user->cards;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Post(
     *      path="/card",
     *      tags={"Tarjetas"},
     *      summary="Registro de una tarjeta",
     *      description="Registro de una tarjeta",
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   ref="#/components/schemas/CardSave"
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Card")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate(
            [
                'token' => 'required',
                'alias' => [
                    'required',
                    'max:100',
                    Rule::unique('cards')->where(function ($query) {
                        return $query->where('user_id', auth()->user()->id)
                                ->where('active',1);
                    })
                ],
                'number' => 'required|digits:4',
                'type' => 'required',
            ],
            [
                'alias.unique' => 'Ya has registrado una tarjeta con el mismo alias'
            ]
        );

        $data['active'] = true;

        $card = auth()->user()->cards()->create($data);

        $card->append(['number_formatted','type_formatted']);

        return $card;
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Put(
     *      path="/card",
     *      tags={"Tarjetas"},
     *      summary="Actualizar alias de una tarjeta",
     *      description="Actualizar alias de una tarjeta",
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   @OA\Property(property="alias", type="string")
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Card")
     *      ),
     *      @OA\Response(
     *           response="403", description="El usuario que realizo la petición no es dueño de la tarjeta",
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $card = Card::findOrFail($id);

        abort_unless(auth()->user()->id === $card->user_id, 403);

        $data = $request->validate(
            [
                'alias' => [
                    'required',
                    'max:100',
                    Rule::unique('cards')->where(function ($query) {
                        return $query->where('user_id', auth()->user()->id)
                                ->where('active',1);
                    })
                ],
            ],
            [
                'alias.unique' => 'Ya has registrado una tarjeta con el mismo alias'
            ]
        );


        
        $card->alias = $data['alias'];
        $card->save();

        return $card;
    }

    /**
     * @OA\Delete(
     *      path="/card/{id}",
     *      tags={"Tarjetas"},
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la tarjeta",
     *         required=true,
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *      ),
     *      @OA\Response(
     *           response="403", description="El usuario que realizo la petición no es dueño de la tarjeta",
     *      ),
     *      @OA\Response(
     *           response="412", description="Se tiene una rifa activa, no se puede borrar la tarjeta",
     *      ),
     *      summary="Eliminar una tarjeta",
     *      description="Eliminar una tarjeta, solo la puede eliminar el usuario que posee esa tarjeta y no tenga una rifa activa",
     *      security={{"bearerAuth":{}}}
     * )
     */
    public function destroy(Request $request,Card $card)
    {
        abort_unless(auth()->user()->id === $card->user_id, 403);

        $permissions = $request->user()
                ->getPermissionsViaRoles()
                ->pluck('name')
                ->toArray();

        if(in_array('Rifante',$permissions)){
            $cards = $request->user()->cards;

            if(count($cards) == 1){
                $raffles = Raffle::where('user_id',$request->user()->id)
                ->active()
                ->count();

                if($raffles > 0){
                    return response()->json(['message' => 'Raffle active, please add other method before deleted this card'],412);     
                }
            }
        }


        $card->active = false;
        $card->token = '';
        $card->source_id = '';
        $card->save();

        return $card;
    }
}
