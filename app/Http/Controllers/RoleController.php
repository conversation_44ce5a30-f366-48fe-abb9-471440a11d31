<?php

namespace App\Http\Controllers;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

use Illuminate\Http\Request;

class RoleController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Role::with('permissions')->get();
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        
        if(!$request->has('name')){
        	return response(400);
        }

        $role = Role::create(['name' => $request->input('name'),'guard_name' => 'web']);
        $role->syncPermissions($request->input('permissions'));


        return $role;

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
    	$role = Role::findOrfail($id);
    	$role->permissions = $role->permissions()->pluck('name')->toArray();  
        return $role;
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
     	if(!$request->has('name') || !is_numeric($id) || $id <= 0 || !$request->has('permissions')){
        	return response(400);
        }   

        $role = Role::findOrfail($id);
        $role->name = $request->input('name');

        $role->syncPermissions($request->input('permissions'));

        return $role;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
    	$role = Role::findOrfail($id);
        return response()->json($role->delete());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function getPermissions($id)
    {
        return Permission::all();
    }

}
