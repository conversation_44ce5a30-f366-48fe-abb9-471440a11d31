<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RankingEventMultipleSheetExport;
use App\Exports\RankingNationalMultipleSheetExport;
use App\Exports\EventResultMultipleSheetExport;
use App\Exports\MassificationCampusPlayerExport;
use App\Exports\CampusPlayerExport;
use App\Exports\EventPreregisterExport;
use App\Exports\ImportPlayerTemplate;
use App\Exports\ImportPlayerResultTemplate;
use App\Imports\ImportCSV;
use App\Model\Event;
use App\Model\Result;
use App\Model\Campus;
use App\Model\MassificationCampus;
use App\Model\Category;
use App\Model\Test;
use App\Model\Player;

use Carbon\Carbon;
use DB;

use Illuminate\Support\Facades\Log;

class ExportController extends Controller
{
    /*public function rankingEvent($id,$type, $phase = 0){
    	$event = Event::findOrFail($id);
    	$name = 'ranking_'.$event->name.'.xlsx';
    	return Excel::download(new RankingEventMultipleSheetExport($id,$type,$phase), $name);
    }*/

    public function rankingEvent($id, $phase = 0){
        $event = Event::findOrFail($id);
        $name = 'ranking_'.$event->name.'.xlsx';
        return Excel::download(new RankingEventMultipleSheetExport($id,$phase), $name);
    }

    public function rankingNational($type,$cycle){
    	$name = 'ranking_national.xlsx';
    	return Excel::download(new RankingNationalMultipleSheetExport($type,$cycle), $name);
    }

    public function campusPlayers($campus,$start,$end){
    	$campus = Campus::findOrFail($campus);
    	$name = 'players_'.$campus->name.'.xlsx';
    	return Excel::download(new CampusPlayerExport($campus->id,$start,$end), $name);	
    }

    public function campusMassificationPlayers($campus){
        $campus = MassificationCampus::findOrFail($campus);
        $name = 'massification_players_'.$campus->name.'.xlsx';
        return Excel::download(new MassificationCampusPlayerExport($campus->id), $name); 
    }

    public function eventResult($id){
        $event = Event::findOrFail($id);
        $name = 'results_'.$event->name.'.xlsx';
        return Excel::download(new EventResultMultipleSheetExport($id), $name);    
    }


    public function import(){

        $path = 'update_players_id.csv';
        $data = Excel::toArray(new ImportCSV, $path);
        //$data = Excel::load($path,'UTF-8');
        Log::debug("Start $path :". Carbon::now());
        $players = [];
        
        $success = [];
        $error = [];
        foreach ($data[0] as $index => $row) {
            if($index < 1){
                continue; 
            }
            $player = $this->updatePlayerId($row);
            if($player != null){
                $success[] = $player;
            } else {
                $error[] = $row;
            }
        }
        Log::debug("End $path :". Carbon::now());
        return ["errors" => $error, "success" => $success];
    }

    public function importPlayerTemplate(){
        return Excel::download(new ImportPlayerTemplate(), 'import_players.xlsx');
    }

    public function eventPlayerImportTemplate($event,$min,$max){
        $event = Event::findOrFail($event);

        return Excel::download(new ImportPlayerResultTemplate($event->id,$min,$max),'import_result_template.xlsx');
    }

    public function eventPreregister($id){
        $event = Event::findOrFail($id);
        $name = 'preregister_'.$event->name.'.xlsx';
        return Excel::download(new EventPreregisterExport($id), $name); 
    }

    private function updatePlayer($row){
        $date = Carbon::createFromFormat('d/m/y',$row[6]);
        $player = Player::where('first_name',$row[3])
            ->where('middle_name', $row[4] )
            ->where('last_name', $row[5] )
            ->where('birthdate',  $date->toDateString() )
            ->first();

        if(!$player){
            Log::debug('-----Error-----');
            Log::debug('Nombre: '. $row[3] );
            Log::debug('Apellido Paterno: '. $row[4] );
            Log::debug('Apellido Materno: '. $row[5] );
            Log::debug('Fecha nacimiento: '. $date->toDateString() );
            return null;
        }

        $player->league = isset($row[11]) ? $row[11] : '';
        $player->email = isset($row[8]) ? $row[8] : '';
        $player->address = isset($row[10]) ? $row[10] : '' ;
        $player->phone = isset($row[9]) ? $row[9] : '';

        $player->save();

        return $player;
    }

    private function updatePlayerId($row){
        $date = Carbon::createFromFormat('d/m/y',$row[6]);
        $player = Player::findOrFail($row[0]);

        if(!$player){
            Log::debug('-----Error-----');
            Log::debug('Nombre: '. $row[3] );
            Log::debug('Apellido Paterno: '. $row[4] );
            Log::debug('Apellido Materno: '. $row[5] );
            Log::debug('Fecha nacimiento: '. $date->toDateString() );
            return null;
        }

        $player->league = isset($row[11]) ? $row[11] : '';
        $player->email = isset($row[8]) ? $row[8] : '';
        $player->address = isset($row[10]) ? $row[10] : '' ;
        $player->phone = isset($row[9]) ? $row[9] : '';

        $player->save();

        return $player;
    }

    private function getPlayer($row){

        $age = Carbon::parse($row[5])->age;
        
        //
        $category = Category::where('min_age','<=',$age)->where('max_age','>=',$age)->first();
        
        if(!$category){
            $category = Category::where('name','11-12')->first();    
        }

        $player->campus_id = 4; // modificar antes de subir

        $player = new Player;
        $player->first_name = $row[2];
        $player->middle_name = $row[3];
        $player->last_name = $row[4];
        $player->age = $age;
        $player->birthdate = $row[5];
        $player->city = isset($row[17]) ? $row[17] : '';
        $player->category_id = $category->id;
        $player->cat_state_id = 4;
        $player->league = $row[13];
        $player->email = $row[14];
        $player->address = isset($row[16]) ? $row[16] : '' ;
        $player->phone = $row[15];
        
        $player->elegibility = Carbon::parse($row[5])->year + 16; 
        
        //dominant_hand_bat
        if(strtoupper($row[12]) == 'D'){
            $player->cat_dominant_hand_bat_id = 1;
        }else if (strtoupper($row[12]) == 'D'){
            $player->cat_dominant_hand_bat_id = 2;
        }else{
            $player->cat_dominant_hand_bat_id = 3;
        }

        //dominant_hand
        if(strtoupper($row[11]) == 'D'){
            $player->cat_dominant_hand_id = 1;
        }else if ($row[11] == 'D'){
            $player->cat_dominant_hand_id = 2;
        }else{
            $player->cat_dominant_hand_id = 3;
        }

        //principal_position
        if(strtoupper($row[8]) == 'P'){
            $player->principal_position_id = 1;
        }else if (strtoupper($row[8]) == 'C'){
            $player->principal_position_id = 2;
        }else if (strtoupper($row[8]) == 'IF'){
            $player->principal_position_id = 3;
        }else if (strtoupper($row[8]) == 'OF'){
            $player->principal_position_id = 4;
        }

        //principal_position
        if(strtoupper($row[9]) == 'P'){
            $player->secundary_position_id = 1;
        }else if (strtoupper($row[9]) == 'C'){
            $player->secundary_position_id = 2;
        }else if (strtoupper($row[9]) == 'IF'){
            $player->secundary_position_id = 3;
        }else if (strtoupper($row[8]) == 'OF'){
            $player->secundary_position_id = 4;
        }


        $player->save();
        return $player;
    }

    private function convert_utf8($valor='') {

        $utf8_ansi2 = array(
        "\u00c0" =>"À",
        "\u00c1" =>"Á",
        "\u00c2" =>"Â",
        "\u00c3" =>"Ã",
        "\u00c4" =>"Ä",
        "\u00c5" =>"Å",
        "\u00c6" =>"Æ",
        "\u00c7" =>"Ç",
        "\u00c8" =>"È",
        "\u00c9" =>"É",
        "\u00ca" =>"Ê",
        "\u00cb" =>"Ë",
        "\u00cc" =>"Ì",
        "\u00cd" =>"Í",
        "\u00ce" =>"Î",
        "\u00cf" =>"Ï",
        "\u00d1" =>"Ñ",
        "\u00d2" =>"Ò",
        "\u00d3" =>"Ó",
        "\u00d4" =>"Ô",
        "\u00d5" =>"Õ",
        "\u00d6" =>"Ö",
        "\u00d8" =>"Ø",
        "\u00d9" =>"Ù",
        "\u00da" =>"Ú",
        "\u00db" =>"Û",
        "\u00dc" =>"Ü",
        "\u00dd" =>"Ý",
        "\u00df" =>"ß",
        "\u00e0" =>"à",
        "\u00e1" =>"á",
        "\u00e2" =>"â",
        "\u00e3" =>"ã",
        "\u00e4" =>"ä",
        "\u00e5" =>"å",
        "\u00e6" =>"æ",
        "\u00e7" =>"ç",
        "\u00e8" =>"è",
        "\u00e9" =>"é",
        "\u00ea" =>"ê",
        "\u00eb" =>"ë",
        "\u00ec" =>"ì",
        "\u00ed" =>"í",
        "\u00ee" =>"î",
        "\u00ef" =>"ï",
        "\u00f0" =>"ð",
        "\u00f1" =>"ñ",
        "\u00f2" =>"ò",
        "\u00f3" =>"ó",
        "\u00f4" =>"ô",
        "\u00f5" =>"õ",
        "\u00f6" =>"ö",
        "\u00f8" =>"ø",
        "\u00f9" =>"ù",
        "\u00fa" =>"ú",
        "\u00fb" =>"û",
        "\u00fc" =>"ü",
        "\u00fd" =>"ý",
        "\u00ff" =>"ÿ");

        return strtr($valor, $utf8_ansi2);      

    }
}
