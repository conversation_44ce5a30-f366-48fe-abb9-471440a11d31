<?php

namespace App\Http\Controllers;

use App\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;

class AdminUserController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		return User::selectRaw('users.*')
				   ->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				   ->join('roles as r', 'r.id', 'mhr.role_id')
				   ->where('r.name', 'Administrador')
				   ->get();
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$request->validate([
			'email' => 'unique:App\User,email|email'
		]);

		$user = new User();
		$user->fill($request->all());

		$user->password = bcrypt($request->input('password'));
		$user->save();

		$role = Role::where('name', 'Administrador')->first();

		if ($role != null) {
			$user->assignRole($role->name);
		}

		return $user;
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return User::findOrfail($id);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, $id)
	{
		$user = User::findOrfail($id);
		$user->fill($request->all());
		$user->save();

		return $user;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$user = User::findOrfail($id);
		return response()->json($user->delete());
	}
}