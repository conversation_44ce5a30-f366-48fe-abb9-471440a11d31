<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Raffle as RafflesExport;
use App\Exports\Sale as SalesExport;
use App\Exports\Winner as WinnersExport;
use App\Exports\Recharges as RechargesExport;

class ReportController extends Controller
{
    //
    public function raffles(){
        $raffles = DB::select("SELECT 
                        r.id,
                        r.user_id,
                        u.nickname,
                        a.rfc,
                        r.city,
                        cs.name as state,
                        r.name,
                        r.description,
                        r.prize_type,
                        r.product_type,
                        r.finish,
                        r.prize_value,
                        (CASE
                            WHEN rp.id is not null THEN rp.ticket_amount
                            ELSE
                                ((select count(id) from tickets where raffle_id = r.id and status = 'sold') * r.ticket_price ) 
                         END) as amount,
                        (CASE
                            WHEN rp.id is not null THEN rp.commission_amount
                            ELSE
                                ((select count(id) from tickets where raffle_id = r.id and status = 'sold') * r.ticket_price * 0.2 ) 
                         END) as fee_amount,
                        (CASE
                            WHEN rp.id is not null THEN rp.commission_iva
                            ELSE
                                ((select count(id) from tickets where raffle_id = r.id and status = 'sold') * r.ticket_price * 0.2 * 0.16 ) 
                         END) as fee_taxes,
                        (CASE
                            WHEN rp.id is not null THEN rp.isr_amount
                            WHEN a.rfc is not null OR a.rfc != '' THEN ((select count(id) from tickets where raffle_id = r.id and status = 'sold') * r.ticket_price * 0.01 ) 
                            ELSE
                                ((select count(id) from tickets where raffle_id = r.id and status = 'sold') * r.ticket_price * 0.2 ) 
                         END) as isr_taxes,
                        r.num_tickets,
                        r.ticket_price,
                        (select count(id) from tickets where raffle_id = r.id and status = 'sold') as sold_tickets,
                        r.canceled,
                        (select count(id) from payment_orders where raffle_id = r.id and status = 'success') as total_orders,
                        (select count(id) from payment_orders where raffle_id = r.id and status = 'success' and payment_type = 'conekta')  as conekta_orders,
                        (select count(id) from payment_orders where raffle_id = r.id and status = 'success' and payment_type = 'rafiki')  as rafiki_orders,
                        (select sum(commission) from payment_orders where raffle_id = r.id and status = 'success' and payment_type = 'conekta')  as conekta_fees,
                        (CASE
                            WHEN rp.status is not null THEN rp.status  
                            ELSE
                                'not_generated' 
                         END) as payment_status,
                        (select count(id) from tickets where raffle_id = r.id and status = 'sold') / (select count(id) from payment_orders where raffle_id = r.id and status = 'success')  as avg_ticket_order
                    FROM rafiki.raffles r
                        left join users u on u.id = r.user_id 
                        left join cat_states cs on cs.id = r.cat_state_id
                        left join raffler_payments rp on rp.raffle_id = r.id and ( rp.status = 'pending' or rp.status = 'accepted') 
                        left join applications a on u.id = a.user_id and a.approval = 1");

        return $raffles;
    }

    public function exportRaffles(){
        $name = 'raffles.xlsx';
        return Excel::download(new RafflesExport(), $name);
    }

    public function sales(){
        $sales = DB::select("SELECT 
                    po.buyer_id as user_id,
                    u.nickname as user,
                    po.raffle_id,
                    r.name raffle_name,
                    po.created_at as date,
                    po.num_tickets,
                    po.payment_type,
                    po.payment_total,
                    po.commission as fee
                FROM 
                    payment_orders as po
                LEFT JOIN users as u on po.buyer_id = u.id 
                LEFT JOIN raffles as r on po.raffle_id = r.id 
                where po.status = 'success'
                order by po.raffle_id, po.created_at");

        return $sales;

    }

    public function exportSales(){
        $name = 'sales.xlsx';
        return Excel::download(new SalesExport(), $name);
    }


    public function winners(){
        $winners = DB::select("SELECT 
                rw.winner_id as winner_id,
                u.nickname as winner,
                rw.raffle_id,
                r.name,
                (select count(id)  from tickets where rw.raffle_id = tickets.raffle_id and tickets.status = 'sold' and tickets.buyer_id = rw.winner_id) as total_tickets,
                (select count(id)  from tickets where rw.raffle_id = tickets.raffle_id and tickets.status = 'sold' and tickets.buyer_id = rw.winner_id) * r.ticket_price as total_amount
            FROM 
                raffle_winners as rw
            LEFT JOIN users as u on u.id = rw.winner_id
            LEFT JOIN raffles as r on r.id = rw.raffle_id");

        return $winners;
    }

    public function exportWinners(){
        $name = 'winners.xlsx';
        return Excel::download(new WinnersExport(), $name);
    }

    public function recharges(){
        $recharges = DB::select("SELECT 
                        b.id,
                        b.amount,
                        b.commission,
                        u.name,
                        u.last_name,
                        u.nickname,
                        b.user_id,
                        b.created_at
                    FROM 
                        balances as b
                    LEFT JOIN users as u on u.id = b.user_id
                    where card_id is not null
                    order by b.created_at desc");

        return $recharges;
    }

    public function exportRecharges(){
        $name = 'wallet-recharges.csv';
        return Excel::download(new RechargesExport(), $name);
    }
}
