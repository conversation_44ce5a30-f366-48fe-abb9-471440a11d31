<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Model\Raffle;
use App\Model\RafflePhoto;
use App\Model\RaffleWinner;
use App\Model\Ticket;
use App\Model\Card;
use App\Model\RaffleDraw;
use App\Model\Notification;
use App\Helpers\AppHelper;
use App\Http\Requests\RaffleRequest;
use Carbon\Carbon;
use App\User;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Mail;
use App\Mail\Winner;
use App\Mail\RaffleDraw as DrawMail;
use App\Mail\CancelRaffle;

class RaffleController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @OA\Get(
	 *      path="/raffle",
	 *      tags={"Rifas"},
	 *      summary="Listado de rifas para los usuarios en general",
	 *      description="Listado de rifas para los usuarios en general",
	 *      @OA\Parameter(
	 *         name="page",
	 *         in="path",
	 *         description="Número de página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="size",
	 *         in="path",
	 *         description="Tamaño de la página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="search",
	 *         in="path",
	 *         description="Campo de busqueda",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="category",
	 *         in="path",
	 *         description="Id de la categoría",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="subcategory",
	 *         in="path",
	 *         description="Id de la subcategoría",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="start-price",
	 *         in="path",
	 *         description="Precio mínimo del boleto",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="end-price",
	 *         in="path",
	 *         description="Precio máximo del boleto",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="order",
	 *         in="path",
	 *         description="Ordenar la consulta por la columna mencionada. Campos permitidos 'finish','num_tickets' y 'ticket_price'. Default 'finish'",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="order-way",
	 *         in="path",
	 *         description="Ordenar la consulta por la columna mencionada en sentido ascendente o descendente. Campos permitidos 'asc' y 'desc'. Default 'desc'",
	 *         required=false,
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RafflePaginated")
	 *      ),
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index(Request $request)
	{
		$orderColumns = ['finish', 'num_tickets', 'ticket_price'];
		$orderWays = ['asc', 'desc'];


		$page = $request->has('page') ? $request->input('page') : 1;
		$size = $request->has('size') ? $request->input('size') : 20;
		$search = $request->has('search') ? $request->input('search') : null;
		$startPrice = $request->has('start-price') ? $request->input('start-price') : null;
		$endPrice = $request->has('end-price') ? $request->input('end-price') : null;
		$category = $request->has('category') ? $request->input('category') : null;
		$subcategory = $request->has('subcategory') ? $request->input('subcategory') : null;

		$order = $request->has('order') ? $request->input('order') : 'finish';
		$orderWay = $request->has('order-way') ? $request->input('order-way') : 'desc';

		if ($order != null) {
			$order = (in_array($order, $orderColumns)) ? $order : null;
		}

		if ($orderWay != null) {
			$orderWay = (in_array($orderWay, $orderWays)) ? $orderWay : null;
		}

		return Raffle::select('*')
			->where('finish', '>=', Carbon::now())
			->with(['images'])
			->active()
			->where(function ($query) use ($search) {
				if ($search != null) {
					$query->where('name', 'like', "%" . $search . "%");
				}
			})
			->where(function ($query) use ($startPrice) {
				if ($startPrice != null) {
					$query->where('ticket_price', '>=', $startPrice);
				}
			})
			->where(function ($query) use ($endPrice) {
				if ($endPrice != null) {
					$query->where('ticket_price', '<=', $endPrice);
				}
			})
			->where(function ($query) use ($subcategory) {
				if ($subcategory != null) {
					$query->where('subcategory_id', $subcategory);
				}
			})
			->whereHas('subcategory', function ($query) use ($category) {
				if ($category != null) {
					$query->where('category_id', $category);
				}
			})
			->orderBy($order, $orderWay)
			->paginate($size, $page);
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @OA\Get(
	 *      path="/raffle/results",
	 *      tags={"Rifas"},
	 *      summary="Listado de rifas ejecutadas",
	 *      description="Listado de rifas ejecutadas",
	 *      @OA\Parameter(
	 *         name="page",
	 *         in="path",
	 *         description="Número de página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="size",
	 *         in="path",
	 *         description="Tamaño de la página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="search",
	 *         in="path",
	 *         description="Campo de busqueda",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="category",
	 *         in="path",
	 *         description="Id de la categoría",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="subcategory",
	 *         in="path",
	 *         description="Id de la subcategoría",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="start-price",
	 *         in="path",
	 *         description="Precio mínimo del boleto",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="end-price",
	 *         in="path",
	 *         description="Precio máximo del boleto",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="order",
	 *         in="path",
	 *         description="Ordenar la consulta por la columna mencionada. Campos permitidos 'finish','num_tickets' y 'ticket_price'. Default 'finish'",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="order-way",
	 *         in="path",
	 *         description="Ordenar la consulta por la columna mencionada en sentido ascendente o descendente. Campos permitidos 'asc' y 'desc'. Default 'desc'",
	 *         required=false,
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RafflePaginated")
	 *      ),
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function results(Request $request)
	{
		$orderColumns = ['finish'];
		$orderWays = ['asc', 'desc'];


		$page = $request->has('page') ? $request->input('page') : 1;
		$size = $request->has('size') ? $request->input('size') : 20;
		$search = $request->has('search') ? $request->input('search') : null;
		$startPrice = $request->has('start-price') ? $request->input('start-price') : null;
		$endPrice = $request->has('end-price') ? $request->input('end-price') : null;
		$category = $request->has('category') ? $request->input('category') : null;
		$subcategory = $request->has('subcategory') ? $request->input('subcategory') : null;

		$order = $request->has('order') ? $request->input('order') : 'finish';
		$orderWay = $request->has('order-way') ? $request->input('order-way') : 'desc';

		if ($order != null) {
			$order = (in_array($order, $orderColumns)) ? $order : null;
		}

		if ($orderWay != null) {
			$orderWay = (in_array($orderWay, $orderWays)) ? $orderWay : null;
		}

		return Raffle::select('raffles.*')
			->join('raffle_winners', 'raffles.id', 'raffle_winners.raffle_id')
			->with(['images'])
			->where(function ($query) use ($search) {
				if ($search != null) {
					$query->where('name', 'like', "%" . $search . "%");
				}
			})
			->where(function ($query) use ($startPrice) {
				if ($startPrice != null) {
					$query->where('ticket_price', '>=', $startPrice);
				}
			})
			->where(function ($query) use ($endPrice) {
				if ($endPrice != null) {
					$query->where('ticket_price', '<=', $endPrice);
				}
			})
			->where(function ($query) use ($subcategory) {
				if ($subcategory != null) {
					$query->where('subcategory_id', $subcategory);
				}
			})
			->whereHas('subcategory', function ($query) use ($category) {
				if ($category != null) {
					$query->where('category_id', $category);
				}
			})
			->orderBy($order, $orderWay)
			->paginate($size, $page);
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle",
	 *      tags={"Rifas"},
	 *      summary="Guardar una rifa",
	 *      description="Guardar una rifa",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleSave"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Raffle")
	 *      ),
	 *      @OA\Response(
	 *           response="412", description="Invalid User | Invalid ticket price | Invalid prize value| Card active is required to create a raffle ",
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(RaffleRequest $request)
	{

		try {
			DB::beginTransaction();

			$permissions = $request->user()
				->getPermissionsViaRoles()
				->pluck('name')
				->toArray();


			if (!in_array('Rifante', $permissions)) {
				return response()->json(['message' => 'Invalid User'], 412);
			}

			//TODO: cambiar para la prueba
			if ($request->input('ticket_price') < 50) {
				return response()->json(['message' => 'Invalid ticket price'], 412);
			}

			//TODO: cambiar para la prueba
			if ($request->input('ticket_price') * $request->input('num_tickets') < ($request->input('prize_value') * 2.5)) {
				return response()->json(['message' => 'Invalid prize value'], 412);
			}

			//TODO: Remover si se quiere quitar la validacion con tarjeta
			if ($request->user()->cards()->count() == 0) {
				return response()->json(['message' => 'Card active is required to create a raffle'], 412);
			}

			$raffle = new Raffle;
			$raffle->fill($request->all());
			$raffle->user_id = $request->user()->id;
			$raffle->save();

			if ($request->has('images')) {
				foreach ($request->input('images') as $image) {
					$act_photo = new RafflePhoto();
					$act_photo->url_image = $image['url_image'];
					$act_photo->default = $image['default'];
					$act_photo->raffle_id = $raffle->id;
					$act_photo->save();
				}
			}

			if ($raffle->active) {
				$tickets = $this->createTickets($raffle);
			}

			DB::commit();
		} catch (\PDOException $e) {
			DB::rollBack();
			return  $e;
		}

		return $raffle;
	}

	private function createTickets($raffle)
	{
		$tickets = [];
		for ($i = 0; $i < $raffle->num_tickets; $i++) {
			$ticket = new Ticket;
			$ticket->number = $i + 1;
			$ticket->locked = false;
			$ticket->status = 'Free';
			$ticket->raffle_id = $raffle->id;
			$ticket->save();

			$tickets[] = $ticket;
		}

		return $tickets;
	}

	/**
	 * Display the specified resource.
	 *
	 *
	 * @OA\Get(
	 *      path="/raffle/{id}",
	 *      tags={"Rifas"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la rifa",
	 *         required=true,
	 *      ),
	 *      summary="Obtener la información de la rifa",
	 *      description="Obtener la información de la rifa",
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleTickets")
	 *      ),
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return Raffle::with(['subcategory', 'images', 'tickets'])->where('id', $id)->first()->append('tops');
	}

	/**
	 * Display the specified resource.
	 *
	 *
	 * @OA\Get(
	 *      path="/raffle/{id}/winner-detail",
	 *      tags={"Rifas"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la rifa",
	 *         required=true,
	 *      ),
	 *      summary="Obtener la información de la rifa con la informacion del ganador",
	 *      description="Obtener la información de la rifa del ganador",
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleWinnerDetail")
	 *      ),
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function winnerDetail($id)
	{
		return Raffle::with(['raffleWinner.address', 'images', 'digitalPrize'])->where('id', $id)->first();
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Put(
	 *      path="/raffle/{id}",
	 *      tags={"Rifas"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la rifa",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar rifa",
	 *      description="Actualizar rifa",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleSave"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Raffle")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(RaffleRequest $request, $id)
	{

		try {
			DB::beginTransaction();
			$raffle = Raffle::findOrFail($id);

			if ($raffle->active) {
				return response()->json(
					[
						"message" => "Raffle currently active, update not available"
					],
					412
				);
			}


			//TODO: cambiar para la prueba
			if ($request->input('ticket_price') < 50) {
				return response()->json(['message' => 'Invalid ticket price'], 412);
			}

			$last_active = $raffle->active;

			$raffle->fill($request->all());
			$raffle->save();

			RafflePhoto::where('raffle_id', $raffle->id)
				->delete();
			if ($request->has('images')) {
				foreach ($request->input('images') as $image) {
					$act_photo = new RafflePhoto();
					$act_photo->url_image = $image['url_image'];
					$act_photo->default = $image['default'];
					$act_photo->raffle_id = $raffle->id;
					$act_photo->save();
				}
			}

			if ($last_active == false && $raffle->active) {
				$tickets = $this->createTickets($raffle);
			}

			DB::commit();
		} catch (\PDOException $e) {
			DB::rollBack();
			return  $e;
		}



		return $raffle;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$raffle = Raffle::findOrFail($id);
		return response()->json($raffle->delete());
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function cancel(Request $request, Raffle $raffle)
	{

		if( $raffle->canceled == 1){
			return response()->json([
				"error" => "412",
				"message" => "Raffle previously canceled",
			],412);
		}

		$raffle->canceled = 1;
		$raffle->active = 0;


		if($request->has('comments')){
			$raffle->cancel_comments = $request->input('comments');
		}
		$raffle->save();

		return $raffle;
	}

	/**
	 * Display a listing of the resource.
	 *
	 *
	 * @OA\Get(
	 *      path="/raffle/admin",
	 *      tags={"Rifas"},
	 *      summary="Listado de rifas para los administradores o rifantes",
	 *      description="Listado de rifas para los administradores o rifantes",
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="array",
	 *              @OA\Items(ref="#/components/schemas/Raffle")
	 *          )
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function admin(Request $request)
	{
		$user = request()->user();

		if ($user->hasRole('Administrador')) {
			return Raffle::with('images')->orderBy('created_at','desc')->get();
		}

		return Raffle::with('images')->where('user_id', $user->id)->orderBy('created_at','desc')->get();
	}

	/**
	 *
	 * @OA\Post(
	 *      path="/raffle/upload-photo",
	 *      tags={"Rifas"},
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="multipart/form-data",
	 *               @OA\Schema(
	 *                   required={"file"},
	 *                   @OA\Property(
	 *                       property="file",
	 *                       type="string",
	 *                       format="binary",
	 *                   )
	 *               ),
	 *           ),
	 *      ),
	 *      summary="Subir una imagen para la rifa",
	 *      description="Subir una imagen para la rifa",
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              @OA\Property(
	 *                  property="url",
	 *                  type="string"
	 *              )
	 *           )
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function uploadPhoto(Request $request)
	{
		$url = "";
		if (request()->hasFile('file')) {
			$url = $this->uploadFile(
				$request->file('file'),
				Carbon::now()->timestamp . '.' . request()->file('file')->getClientOriginalExtension(),
				'rafiki/activity-image'
			);
		}

		return response()->json(["url" => $url]);
	}

	/**
	 * Display a listing of the resource.
	 *
	 * @OA\Get(
	 *      path="/raffle/record",
	 *      tags={"Rifas"},
	 *      summary="Listado de rifas en las que el cliente ha comprado boletos",
	 *      description="Listado de rifas en las que el cliente ha comprado boletos",
	 *      @OA\Parameter(
	 *         name="page",
	 *         in="path",
	 *         description="Número de página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="size",
	 *         in="path",
	 *         description="Tamaño de la página",
	 *         required=false,
	 *      ),
	 *      @OA\Parameter(
	 *         name="search",
	 *         in="path",
	 *         description="Campo de busqueda",
	 *         required=false,
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="array",
	 *              @OA\Items(ref="#/components/schemas/RaffleRecordPaginated")
	 *          )
	 *      ),
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function record(Request $request)
	{

		$page = $request->has('page') ? $request->input('page') : 1;
		$size = $request->has('size') ? $request->input('size') : 20;
		$search = $request->has('search') ? $request->input('search') : null;

		$user = $request->user();

		$raffles = Raffle::with(['images', 'buyed_tickets'])
			->select('raffles.*')
			->leftJoin(
				DB::raw("(select buyer_id,raffle_id,max(buy_date) as buy_date
				 from tickets where buyer_id is not null group by buyer_id,raffle_id) as t")
				,'t.raffle_id','=','raffles.id')
			->where('buyer_id',$user->id)
			->where(function ($query) use ($search) {
				if ($search != null) {
					$query->where('raffles.name', 'like', "%" . $search . "%");
				}
			})
			->orderBy('buy_date','desc')
			->paginate($size, $page);


		return $raffles;
	}

	public function winner($id)
	{
		$raffle = Raffle::whereDoesntHave('draw')->where('id', $id)->first();

		if ($raffle == null) {
			return response(["message" => "Rifa inexistente o con sorteo realizado"], 412);
		}

		$winner = $this->draw($raffle);

		return $winner;
	}

	public function draw($raffle)
	{
		$draw = new RaffleDraw;
		$draw->raffle_id = $raffle->id;
		$draw->start = Carbon::now();

		$draw->save();

        $tickets = Ticket::where('raffle_id', $raffle->id)
        ->where('status', 'Sold')
        //->where('buyer_id', 902)    // <----- USUARIO GANADOR
        ->get();

		$raffle->winner = $this->ticketWinner($raffle, $tickets);

		$draw->winner_ticket_id = $raffle->winner->id;
		$draw->end = Carbon::now();
		$draw->finished = 1;
		$draw->save();

		return $raffle->winner;
	}

	private function ticketWinner($raffle, $tickets)
	{
		$count = $tickets->count();

		if ($count == 1) {
			$ticket = $tickets[0];

			$ticket->winner = 1;
			$ticket->selection_number = $count;
			$ticket->selection_time = Carbon::now();
			$ticket->save();

			$winner = RaffleWinner::where('raffle_id', $ticket->raffle_id)->first();

			if ($winner == null) {
				$winner = new RaffleWinner;
			}

			$data = [
				'click_action' => "FLUTTER_NOTIFICATION_CLICK",
				'raffle_id' => $raffle->id,
				'raffle_name' => $raffle->name,
				'user_id' => $ticket->buyer_id,
			];

			Notification::create([
				'title'   => 'Ganador de la rifa',
				'message' => '¡Felicidades! Boleto ganador de ' . $raffle->name,
				'data'    => $raffle->toJson(),
				'user_id' => $ticket->buyer_id,
				'cat_notification_type_id' => 3
			]);

			AppHelper::sendPushNotification(
				$ticket->buyer_id,
				'Ganador de la rifa',
				'¡Felicidades! Boleto ganador de ' . $raffle->name,
				$data
			);

			$user = User::find($ticket->buyer_id);


			Mail::to($user->email)
				->send(
					new Winner($user, $raffle)
				);


			$data = [
				'click_action' => "FLUTTER_NOTIFICATION_CLICK",
				'raffle_id' => $raffle->id,
				'raffle_name' => $raffle->name,
				'user_id' => $ticket->user_id,
			];

			Notification::create([
				'title'   => 'Ganador de la rifa seleccionado',
				'message' => 'Se selecciono el boleto ganador de la rifa ' . $raffle->name,
				'data'    => $raffle->toJson(),
				'user_id' => $raffle->user_id,
				'cat_notification_type_id' => 4
			]);

			AppHelper::sendPushNotification(
				$raffle->user_id,
				'Ganador de la rifa seleccionado',
				'Se selecciono el boleto ganador de la rifa ' . $raffle->name,
				$data
			);

			$raffler = User::find($raffle->user_id);

			Mail::to($raffler->email)
				->send(
					new DrawMail($raffler, $raffle)
				);

			$raffle->active = 0;
			$raffle->save();

			$winner->winner_id = $ticket->buyer_id;
			$winner->raffle_id = $ticket->raffle_id;
			$winner->ticket_id = $ticket->id;

			$winner->save();

			return $ticket;
		}

		$random = rand(0, $count - 1);

		$ticket = $tickets[$random];

		$ticket->winner = 0;
		$ticket->selection_number = $count;
		$ticket->selection_time = Carbon::now();
		$ticket->save();

		$tickets->splice($random, 1);

		return $this->ticketWinner($raffle, $tickets);
	}

	public function cancelNotifications($raffle){
		$raffle = Raffle::findOrFail($raffle);

		$raffle->cancelNotifications();

		return $raffle;

	}
}
