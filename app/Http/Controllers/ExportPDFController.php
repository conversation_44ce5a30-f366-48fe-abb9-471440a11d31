<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Player;
use App\Model\Event;
use App\Model\CatPositionError;
use App\Model\Result;
use App\Model\Test;
use App\Model\PlayerErrorEvent;
use App\Model\ResultPhoto;
use App\Model\ReportRankEvent;

use Carbon\Carbon;

class ExportPDFController extends Controller
{
    public function player($id,$event){
	    $player = Player::findOrFail($id);

	    $title = $player->number ? $player->number.'-'.$player->fullName : $player->fullName;

	    $event = Event::findOrFail($event);

	    $positions = CatPositionError::all();

      $rankEvent = ReportRankEvent::where('player_id',$id)->where('event_id',$event->id)->first();

        $tests = Test::where('active',1)->get();

        $testIds = ResultPhoto::where('player_id',$id)
                    ->where('event_id',$event->id)
                    ->get()
                    ->pluck('test_id');

        $photoTests = null;

        
        if(count($testIds) > 0){
            $photoTests = Test::setEagerLoads([])->whereIn('id',$testIds)->get();
            foreach ($photoTests as $test) {
                $test->photos = ResultPhoto::where('player_id',$id)
                        ->where('event_id',$event->id)
                        ->where('test_id',$test->id)
                        ->get();
            }
        }

        $results = Result::where('player_id',$id)
            ->where('event_id',$event->id)
            ->orderBy('test_id')
            ->orderBy('points','desc')
            ->get();
        $weight = '--';
        $size = '--';
        $yards = '--';
        $five = '--';
        $jump = '--';
        $flex = '--';
        $batv = '--';
        $bath = '--';
        $armp = '--';
        $defense = '--';
        $percent = '--';
        foreach ($results as $result) {
        	//weight
          if ($result->test_id == 36) {
            $weight = $result->result;
          }

          //size
          if ($result->test_id == 15) {
            $size = $result->result;
          }

          //60 yards
          if ($result->test_id == 4) {
            $yards = $result->result;
          }

          //5-10-5
          if ($result->test_id == 3) {
            $five = $result->result;
          }

          //jump
          if ($result->test_id == 1) {
            $jump = $result->result;
          }

          //flex
          if ($result->test_id == 2) {
            $flex = $result->result;
          }

          // v bateo
          if ($result->test_id == 7) {
            $batv = $result->result;
          }

          // h bateo
          if ($result->test_id == 10) {
            $bath = $result->result;
          }

          //p brazo
          if ($result->test_id == 2) {
            $armp = $result->result;
          }

          //% grasa
          if ($result->test_id == 16) {
            $percent = $result->result;
          }

          //h defensiva
          //1 pitcher
          //2 catcher
          //3 in
          //4 out
          if($player->principal_position_id == 2 ){
            if ($result->test_id == 17) {
                $defense = $result->result;
            }  
          } else if($player->principal_position_id == 3) {
            if ($result->test_id == 8) {
                $defense = $result->result;
            }
          } else if($player->principal_position_id == 4) {
            if ($result->test_id == 18) {
                $defense = $result->result;
            }
          } else if($player->secundary_position_id == 2  && $defense == '--'){
            if ($result->test_id == 17) {
                $defense = $result->result;
            }  
          } else if($player->secundary_position_id == 3 && $defense == '--') {
            if ($result->test_id == 8) {
                $defense = $result->result;
            }
          } else if($player->secundary_position_id == 4 && $defense == '--') {
            if ($result->test_id == 18) {
                $defense = $result->result;
            }
          }
          
        }

        foreach ($positions as &$position) {
            $position->errors = [];
            $playerPosition = 
            PlayerErrorEvent::select('player_error_event.*')
            ->join('cat_error_player','player_error_event.cat_error_player_id','cat_error_player.id')
            ->where('player_id',$id)
            ->where('event_id',$event->id)
            ->where('cat_position_error_id',$position->id)
            ->get();
            $position->errors = $playerPosition;
        }

        $date = Carbon::parse($event->start)->locale('es_ES');

	    $data = [
	        'player' => $player,
	        'event' => $event,
	        'results' => $results,
	        'positions' => $positions,
	        'size' => $size,
	        'weight' => $weight,
          'yards' => $yards,
          'five' => $five,
          'jump' => $jump,
          'flex' => $flex,
          'batv' => $batv,
          'bath' => $bath,
          'arm' => $armp,
          'defense' => $defense,
          'percent' => $percent,
          'tests' => $tests,
          'rank' => $rankEvent,
          'photos' => $photoTests,
          'date' => ucfirst($date->monthName) . ' ' . $date->format('Y'),
	    ];

	    $pdf = \PDF::loadView('pdf/player', $data);
	 
	    return $pdf->download($title.".pdf");
    }
}
