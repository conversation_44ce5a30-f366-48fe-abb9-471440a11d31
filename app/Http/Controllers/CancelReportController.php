<?php

namespace App\Http\Controllers;

use App\Model\CancelReport;

use Illuminate\Http\Request;

class CancelReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return CancelReport::all();
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function indexPaginated(Request $request)
    {
        $orderColumns = ['status', 'tickets_sold', 'commission','created_at'];
        $orderWays = ['asc', 'desc'];


        $page = $request->has('page') ? $request->input('page') : 1;
        $size = $request->has('size') ? $request->input('size') : 20;
        $search = $request->has('search') ? $request->input('search') : null;

        $order = $request->has('order') ? $request->input('order') : 'created_at';
        $orderWay = $request->has('order-way') ? $request->input('order-way') : 'desc';

        if ($order != null) {
            $order = (in_array($order, $orderColumns)) ? $order : null;
        }

        if ($orderWay != null) {
            $orderWay = (in_array($orderWay, $orderWays)) ? $orderWay : null;
        }

        return CancelReport::select('*')
            ->orderBy($order, $orderWay)
            ->paginate($size, $page);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $cancel = CancelReport::with(['orders'])->where('id',$id)->first();
        $cancel->setAppends(['conekta_orders','rafiki_orders']);
        return $cancel;
    }

}
