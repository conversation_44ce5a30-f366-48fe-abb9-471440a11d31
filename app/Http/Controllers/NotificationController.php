<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Notification;

use App\Model\PushNotification;
use Carbon\Carbon;

class NotificationController extends Controller
{
    /**
     * @OA\Get(
     *      path="/notification",
     *      tags={"Notificaciones"},
     *      summary="Obtener las notificaciones no leídas de un usuario",
     *      description="Obtener las notificaciones no leídas de un usuario",
      *      @OA\Parameter(
     *         name="page",
     *         in="path",
     *         description="Número de página",
     *         required=false,
     *      ),
     *      @OA\Parameter(
     *         name="size",
     *         in="path",
     *         description="Tamaño de la página",
     *         required=false,
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *          @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/NotificationPaginated")
     *          )
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {   
        $page = $request->has('page') ? $request->input('page') : 1;
        $size = $request->has('size') ? $request->input('size') : 20;

        $user = auth()->user()->id;
        return Notification::select('*')
        ->where(
            [
                "user_id" => $user,
                "readed" => 0
            ])
        ->orderBy('created_at','desc')
        ->paginate($size,$page);
    }

    /**
     * @OA\Get(
     *      path="/notification/show-all",
     *      tags={"Notificaciones"},
     *      summary="Obtener las notificaciones",
     *      description="Obtener las notificaciones",
     *      @OA\Parameter(
     *         name="page",
     *         in="path",
     *         description="Número de página",
     *         required=false,
     *      ),
     *      @OA\Parameter(
     *         name="size",
     *         in="path",
     *         description="Tamaño de la página",
     *         required=false,
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *          @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/NotificationPaginated")
     *          )
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function showAll(Request $request)
    {   
        $user = auth()->user()->id;

        $page = $request->has('page') ? $request->input('page') : 1;
        $size = $request->has('size') ? $request->input('size') : 20;

        return Notification::select('*')
            ->where("user_id",$user)
            ->orderBy('created_at','desc')
            ->paginate($size,$page);
        
    }

    /**
     * @OA\Put(
     *      path="/notification/read/{id}",
     *      tags={"Notificaciones"},
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la notificación",
     *         required=true,
     *      ),
     *      summary="Marcar una notificación como leída",
     *      description="Marcar una notificación como leída",
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Notification")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function read($id)
    {   
        $notification = Notification::findOrFail($id);
        $notification->readed = 1;
        $notification->save();
        return $notification;
    }

    /**
     * @OA\Put(
     *      path="/notification/read-all",
     *      tags={"Notificaciones"},
     *      summary="Marcar todas las notificaciones del usuario como leídas",
     *      description="Marcar todas las notificaciones del usuario como leídas",
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(
     *                  @OA\Property(
     *                       property="readed",
     *                       type="number",
     *                   ),
     *          )
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function readAll()
    {   
        $user = auth()->user()->id;
        $notifications = Notification::where(
                [
                    'user_id' => $user,
                    'readed' => 0
                ]
            )
            ->update(
                ['readed' => 1]
            );
        
        return [ "readed" => $notifications ];
    }

    /**
     * @OA\Post(
     *      path="/notification/test",
     *      tags={"Notificaciones"},
     *      summary="Se crea una notificación fake para el usuario logueado",
     *      description="Se crea una notificación fake para el usuario logueado",
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Notification")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function test(Request $request)
    {   

        $user = auth()->user()->id;

        $response = PushNotification::send(
            $user,
            'Notificacion de prueba',
            'Mensaje de prueba', 
            [
                'click_action' => "FLUTTER_NOTIFICATION_CLICK",
                'id' => 1,
                'status' => "TEST",
                'foo' => "TEST",
            ]
        );
        
        $notification = new Notification;
        $notification->title = 'Notificacion de prueba';
        $notification->message = 'Mensaje de prueba';
        $notification->user_id = $user;
        $notification->data = json_encode(["data" => [], "push_response" => $response]);
        $notification->created_at = Carbon::now();
        $notification->cat_notification_type_id = 1;
        $notification->save();
        
        return $notification;
    }
}
