<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Model\PaymentOrder;
use App\Model\Balance;
use App\Model\BalanceLog;
use App\Model\Card;
use App\Model\_Conekta;

use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;

use DB;

class BalanceController extends Controller
{
    //Payment status
	private $error = 'error';
	private $success = 'success';

	//conekta variables
	private $conekta_fixed_commission_mxn = 2.5;
	private $conekta_percentage_commission = 2.9;
	private $iva_percentage = 116;

	/**
	 * @OA\Post(
	 *      path="/wallet/deposit",
	 *      tags={"Monedero Rafiki"},
	 *      summary="Deposito al monedero rafiki",
	 *      description="Deposito al monedero rafiki",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   required={"card_id","amount"},
	 *                   @OA\Property(
	 *                       property="card_id",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="amount",
	 *                       type="string",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", 
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Balance")
	 *      ),
	 *      @OA\Response(
	 *           response="500", 
	 *           description="Error al procesar el pago",
	 *      ),
	 *      @OA\Response(
	 *           response="422", 
	 *           description="Error en la petición",
	 *      ),
	 * )
	 */
    public function deposit(Request $request){
    	$user = $request->user();

    	$data = $request->validate([
			'card_id' => [
					'required',
					Rule::exists('cards','id')->where('active', true)->where('user_id',$user->id),
				],
			'amount' => 'required|integer',
		]);

		$card = Card::findOrFail($request->input('card_id'))
				->makeVisible(['token', 'source_id']);

		$data = (object) [
			"user" => $user->makeVisible(['conekta_source_id']),
			"card" => $card,
			"total" => $request->input('amount'),
			"id" => 1,
		];

		try {
			$response = _Conekta::_createCustomer($data);
		} catch (\Exception $ex) {
			Log::debug($ex->getMessage());

			$this->createBalanceLog(
				$user,
				$data,
				$ex->getMessage(),
				$this->error,
				$card,
				null
			);


			return response()->json($ex->getMessage(), 500);
		}

		try {
			DB::beginTransaction();
			//realizar el cargo correspondiente mediante conekta
			$response = _Conekta::_createOrder($response);

			$total = (int)$request->input('amount');
			$commission = 0;

	    	//calcular comision de conekta
			$commission = (
	    			$this->conekta_fixed_commission_mxn + 
	            	($total * ($this->conekta_percentage_commission / 100))
	        	) *
	        	($this->iva_percentage / 100);

	        $balance = new Balance;

	        $balance->amount = $total;
	        $balance->commission = $commission;
	        $balance->points = 0;
	        $balance->card_id = $card->id;
	        $balance->user_id = $user->id;

	        $balance->save();

	        $this->createBalanceLog(
				$user,
				$data,
				$response,
				$this->success,
				$card,
				$balance
			);

			DB::commit();
		} catch (\Exception $ex) {
			$this->createBalanceLog(
				$user,
				$response,
				$ex->getMessage(),
				$this->error,
				$card,
				null
			);
			return response()->json($ex->getMessage(), 500);
		}
		    	
    	return $balance;
    }

    private function createBalanceLog($user,$data,$log,$status,$card,$balance){    	
    	$balance_log = new BalanceLog;
    	$balance_log->log = json_encode($log);
        $balance_log->data = json_encode($data);
        $balance_log->status = $status;
        $balance_log->balance_id = $balance != null ? $balance->id : null;
        $balance_log->card_id = $card->id;
        $balance_log->user_id = $user->id;

        $balance_log->save();
    	
    	return $balance_log;
        
    }

    /**
	 * @OA\Get(
	 *      path="/wallet",
	 *      tags={"Monedero Rafiki"},
	 *      summary="Obtener el estatus del monedero rafiki saldo, depositos y pagos",
	 *      description="Obtener el estatus del monedero rafiki saldo, depositos y pagos",
	 *      @OA\Response(
	 *           response="200", 
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Wallet")
	 *      ),
	 * )
	 */
    public function wallet(Request $request){
    	$user = $request->user();

    	$balances = Balance::select([
    			'id',
    			'amount',
    			'created_at',
    			])
    			->where('user_id', $user->id)
    			->get();

    	$balances_sum = $balances->sum('amount');

    	$payments = PaymentOrder::select([
    			'id',
    			'num_tickets',
    			'payment_total',
    			'created_at',
    			])
    			->where('buyer_id', $user->id)
    			->where('payment_type', 'Rafiki')
    			->where('status', 'success')
    			->get();

    	$payments_sum = $payments->sum('payment_total');

    	$total = $balances_sum - $payments_sum;

    	return [
    		"deposits" => $balances,
    		"payments" => $payments,
    		"total" => $total,
    	];	
    }
}
