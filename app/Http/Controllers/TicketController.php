<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Ticket;
use Carbon\Carbon;

use App\Model\_Conekta;
use App\Model\Card;
use App\Model\Raffle;
use App\Model\PaymentLog;
use App\Model\PaymentOrder;
use App\Model\TicketPayment;

use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Mail;
use App\Mail\PaymentOrder as PaymentOrderMail;

use DB;

class TicketController extends Controller
{
	//Payment status
	private $error = 'error';
	private $success = 'success';

	//Payment type
	private $conekta = 'Conekta';
	private $rafiki = 'Rafiki';
	private $points = 'Points';

	//conekta variables

	private $conekta_fixed_commission_mxn = 2.5;
	private $conekta_percentage_commission = 2.9;
	private $iva_percentage = 116;

	/**
	 * @OA\Put(
	 *      path="/ticket/lock/{id}",
	 *      tags={"Boletos"},
	 *      summary="Bloquear boleto un boleto",
	 *      description="Bloquear un boleto",
	 *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Ticket")
     *      ),
     *      @OA\Response(
     *           response="412", description="Ticket bloqueado actualmente o comprado"
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
    public function lock(Request $request, $id){
    	$user = $request->user();

    	$ticket = Ticket::findOrFail($id);

    	if(($ticket->locked == 1 && $user->ticket_locked_finish > $ticket->locked_finish) || $ticket->status == 'Sold'){
    		return response()->json(["message" => "Ticket already Locked or Sold"],412);
    	}

    	$ticket->buyer_id = $user->id;
    	$ticket->locked = 1;
        $ticket->locked_finish = $user->ticket_locked_finish ? $user->ticket_locked_finish : strtotime("+10 minutes");
    	$ticket->status = 'Locked';

    	$ticket->save();

    	return $ticket;
    }

    /**
	 * @OA\Put(
	 *      path="/ticket/unlock/{id}",
	 *      tags={"Boletos"},
	 *      summary="Bloquear boleto un boleto",
	 *      description="Bloquear un boleto",
	 *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Ticket")
     *      ),
     *      @OA\Response(
     *           response="412", description="Ticket bloqueado por otro usuario o comprado"
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
    public function unlock(Request $request, $id){
    	$user = $request->user();

    	$ticket = Ticket::findOrFail($id);

    	if($ticket->buyer_id != $user->id || $ticket->status == 'Sold'){
    		return response()->json(["message" => "Ticket already locked for another user or unlocked"],412);
    	}

    	$ticket->buyer_id = null;
    	$ticket->locked = 0;
    	$ticket->locked_finish = null;
    	$ticket->status = 'Free';

    	$ticket->save();

    	return $ticket;
    }

    /**
	 * @OA\Post(
	 *      path="/ticket/random",
	 *      tags={"Boletos"},
	 *      summary="Obtener boletos disponibles de una rifa en modo aleatorio",
	 *      description="Obtener boletos disponibles de una rifa en modo aleatorio. Si se tienen boletos bloqueados de esta rifa se liberarán.",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="raffle_id",
	 *                       type="number",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="number",
	 *                       type="number",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Ticket")
     *          )
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
    public function random(Request $request){
    	$user = $request->user();

    	$data = $request->validate([
			'raffle_id' => 'required|exists:raffles,id',
			'number' => 'required|integer',
		]);

    	//Liberar tickets bloqueados por este usuario para esta rifa
		Ticket::where('raffle_id',$request->input('raffle_id'))
			->where('buyer_id',$user->id)
			->where('status','Locked')
			->update([
				"locked" => false,
				"locked_finish" => null,
				"buyer_id" => null,
				"status" => "Free",
			]);

		$tickets = Ticket::where('raffle_id',$request->input('raffle_id'))
			->where('status','Free')
			->get();

		$tickets = $tickets->random($request->input('number'));

		foreach ($tickets as $ticket) {
			$ticket->update([
				"locked" => true,
				"locked_finish" => $user->ticket_locked_finish,
				"buyer_id" => $user->id,
				"status" => "Locked",
			]);
		}

    	return $tickets;
    }


    /**
	 * @OA\Post(
	 *      path="/ticket/payment/{id}",
	 *      tags={"Boletos"},
	 *      summary="Pagar los boletos apartados. Actualmente no procesa pagos.",
	 *      description="Pagar los boletos apartados. Actualmente no procesa pagos.",
	 *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Ticket")
     *          )
     *      ),
     *      @OA\Response(
     *           response="412",
     *           description="Tickets not selected",
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response

    public function payment(Request $request, $raffle){
    	$user = $request->user();


		$tickets = Ticket::where('raffle_id',$raffle)
			->where('buyer_id',$user->id)
			->where('status','Locked')
			->get();

		if(count($tickets) == 0){
			return response()->json(["message" => "Tickets not selected"],412);
		}

		foreach ($tickets as $ticket) {
			$ticket->update([
				"locked" => false,
				"locked_finish" => null,
				"status" => "Sold",
				"buy_date" => Carbon::now()
			]);
		}

    	return $tickets;
    }
    */

    /**
	 * @OA\Post(
	 *      path="/ticket/payment/{id}/order",
	 *      tags={"Boletos"},
	 *      summary="Pagar los boletos apartados de una rifa, tomando en cuenta metodo de pagos.",
	 *      description="Pagar los boletos apartados de una rifa, tomando en cuenta metodo de pagos.",
	 * *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la rifa",
     *         required=true,
     *      ),
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="card_id",
	 *                       type="number",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="method",
	 *                       type="string",
	 *                       example="Conekta | Rafiki",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Ticket")
     *          )
     *      ),
     *      @OA\Response(
     *           response="412",
     *           description="Tickets not selected",
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
    public function order(Request $request, $raffle_id){

    	$user = $request->user();

    	$request->request->add([ 'raffle_id' => $raffle_id]);

    	$data = $request->validate([
			'raffle_id' => [
					'required',
					Rule::exists('raffles','id')->where('active', true),
				],
			'card_id' => [
					'required_if:method,Conekta',
					Rule::exists('cards','id')->where('active', true)->where('user_id',$user->id),
				],
			'method' => 'required|in:Conekta,Rafiki|string',
		]);

		$tickets = Ticket::where('raffle_id',$raffle_id)
			->where('buyer_id',$user->id)
			->where('status','Locked')
			->get();

		if(count($tickets) == 0){
			return response()->json(["message" => "Tickets not selected"],412);
		}

		$raffle = Raffle::findOrFail($raffle_id);

    	$num_tickets = count($tickets);
    	$total = $num_tickets * $raffle->ticket_price;

		$data = null;
		$card = null;
		$response = null;

		//pagar con rafiki (saldo)
		if($request->input('method') == $this->rafiki){
			if($user->rafiki < $total){
				return response()->json(["message" => "Insufficient rafiki balance"],412);
			}

			$response = [
				'current_rafiki' => $user->rafiki,
				'payment' => $total,
				'balance_before_payment' => ($user->rafiki - $total)
			];
		}

		//pagar con conekta
		if($request->input('method') == $this->conekta){
			$card = Card::findOrFail($request->input('card_id'))
				->makeVisible(['token', 'source_id']);

			$data = (object) [
				"user" => $user->makeVisible(['conekta_source_id']),
				"card" => $card,
				"total" => $total,
				"id" => 1,
			];

			//crear informacion necesaria para conekta antes de generar el pago
			try {
				$response = _Conekta::_createCustomer($data);
			} catch (\Exception $ex) {

				$this->createPaymentOrder(
					$raffle,
					$user,
					$data,
					$ex->getMessage(),
					$request->input('method'),
					$this->error,
					$tickets,
					$card
				);


				return response()->json($ex->getMessage(), 500);
			}

			try {
				DB::beginTransaction();
				//realizar el cargo correspondiente mediante conekta
				$response = _Conekta::_createOrder($response);

				DB::commit();
			} catch (\Exception $ex) {
				$this->createPaymentOrder(
					$raffle,
					$user,
					$response,
					$ex->getMessage(),
					$request->input('method'),
					$this->error,
					$tickets,
					$card
				);
				return response()->json($ex->getMessage(), 500);
			}

		}

		//final procesar pago con conekta
		$order = $this->createPaymentOrder(
			$raffle,
			$user,
			$data,
			$response,
			$request->input('method'),
			$this->success,
			$tickets,
			$card
		);

		if(isset($order['error'])){
			return response()->json($order,500);
		}

		return $order;
    }


    private function createPaymentOrder($raffle,$user,$data,$log,$payment_type,$status,$tickets,$card){

    	$num_tickets = count($tickets);
    	$total = $num_tickets * $raffle->ticket_price;
    	$commission = 0;

    	//calcular comision de conekta
    	if($payment_type == $this->conekta){
    		$commission = (
	    			$this->conekta_fixed_commission_mxn +
	            	($total * ($this->conekta_percentage_commission / 100))
            	) *
            ($this->iva_percentage / 100);
    	}

    	try{
    		DB::beginTransaction();
    		$order = new PaymentOrder;

	    	$order->num_tickets = $num_tickets;
	    	$order->payment_total = $total;
	    	$order->commission = $commission;
	    	$order->transaction = $data != null ? $data->id : null;
	    	$order->payment_type = $payment_type;
	    	$order->status = $status;
	    	$order->raffle_id = $raffle->id;
	    	$order->card_id = $card != null ? $card->id : null;
	    	$order->buyer_id = $user->id;

	    	$order->save();

	    	$ticket_orders = [];
	    	$ticket_numbers = [];
	    	foreach ($tickets as $ticket) {
	    		$ticket_order = new TicketPayment;
	    		$ticket_order->ticket_id = $ticket->id;
	    		$ticket_order->payment_order_id = $order->id;
	        	$ticket_order->save();

	        	$ticket_orders[] = $ticket_order;
	        	$ticket_numbers[] = $ticket->number;
	    	}

	    	$paymentLog = new PaymentLog;

	    	$paymentLog->log = json_encode($log);
	    	$paymentLog->data = json_encode([
	    		"data" => $data,
	    		"tickets" => $tickets
	    	]);
	    	$paymentLog->status = $status;
	    	$paymentLog->raffle_id = $raffle->id;
	    	$paymentLog->payment_order_id = $order->id;
	    	$paymentLog->card_id = $card != null ? $card->id : null;

	        $paymentLog->save();

	        DB::commit();

	        if($this->success == $status){
	        	Mail::to($user->email)
				->send(
					new PaymentOrderMail($user,$order,$raffle, implode(',', $ticket_numbers))
				);
	        }

    	}catch(\Exception $e){
			//Mail exception
			Log::debug($e->getMessage());
    	}catch(\Illuminate\Database\QueryException $ex){

		    DB::rollBack();
    		$paymentLog = new PaymentLog;

	    	$paymentLog->log = $ex->getMessage();
	    	$paymentLog->data = json_encode([
	    		"data" => $data,
	    		"tickets" => $tickets
	    	]);
	    	$paymentLog->status = $this->error;
	    	$paymentLog->raffle_id = $raffle->id;
	    	$paymentLog->card_id = $card != null ? $card->id : null;

	        $paymentLog->save();

	        return [ "error" => true, "message" => $ex->getMessage() ];
		}

    	if($this->success == $status){
			//unlock ticket
			foreach ($tickets as $ticket) {
				$ticket->update([
					"locked" => false,
					"locked_finish" => null,
					"status" => "Sold",
					"buy_date" => Carbon::now()
				]);
			}
    	}

    	return $order;

    }
}
