<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

use App\Imports\ImportCSV;
use App\Model\Player;
use App\Model\Category;
use App\Model\CatState;
use App\Model\CatDominantHand;
use App\Model\CatGender;
use App\Model\CatPosition;
use App\Model\Campus;
use App\Model\Test;
use App\Model\Event;
use App\Model\Result;
use App\Model\TestCategory;

use App\Exports\ErrorImportPlayer;
use App\Exports\ImportPlayerTemplate;

use App\Http\Controllers\ResultController;

use Carbon\Carbon;

class ImportController extends Controller
{
    public function downloadPlayerTemplate(){
        return Excel::download(new ImportPlayerTemplate(), 'import_players.xlsx');
    }

    public function player(Request $request){
        $data = Excel::toArray(new ImportCSV, request()->file('file'));

        $errors = [];
        $players = [];

        foreach ($data[0] as $index => $row) {
            if($index < 2){
                continue; 
            }
            $player = $this->getPlayer($row);    

            if(is_string($player) || $player == false) {
                if($player == false ){
                    $player = 'Complete los campos obligatorios';
                }
            	$errors[] = ["error" => $player, "row" => $row];
            	continue;
            }

            $players[] = $player;
        }

        return ["players" => $players, "errors" => $errors];
    }

    public function result(Request $request){
        $data = Excel::toArray(new ImportCSV, request()->file('file'));
        $tests = [];
        $testErrors = [];
        $results = [];

        $resultController = new ResultController();

        $event_id = $request->input('event_id');
        $event = Event::findOrFail($event_id);

        foreach ($data[0][0] as $index => $value) {
            if($index < 4){
                $test[$index] = null;
                continue;
            }
            $test = Test::where('name',$value)->first();

            if(!$test){
                $testErrors[] = $value;
                continue;
            }

            $tests[$index] = $test;
        }

        if(count($testErrors) > 0){
            return ["testErrors" => $testErrors];
        }

        foreach ($data[0] as $index => $row) {
            if($index == 0){
                continue;
            }
            $player_id = $row[0];
            $category = Category::where('name',$row[3])->first();
            foreach ($row as $key => $value) {
                if($key <= 3 || $value == null){
                    continue;
                }

                $test = $tests[$key]->id;

                $result = new Result;
                $result->result = $value;
                $result->category_id = $category->id;
                $result->player_id = $player_id;
                $result->test_id = $test;
                $result->event_id = $event_id;
                $result->cat_cycle_id = $event->cat_cycle_id;

                $testCategory = TestCategory::where(
                    [
                        'test_id' => $test,
                        'category_id' => $category->id,
                    ]
                )->first();

                

                $controller = new ResultController();


                $player = Player::find($player_id);

                $range = $controller->getRange($testCategory->id,$value);

                $result = $controller->setPonderationRange($result,$player,$range);

                $result->save();

                $resultController->calcBestResult($event_id, $test, $player_id);

                $results[] = $result;

            }
        }

        return $results;
    }

    private function getPlayer($player){
    	if($this->isValidPlayer($player) == false){
    		return false;
    	}

        if(strpos($player[5], '=') !== false){
            return "El campo edad contiene formulas, debe contener un valor numérico.";
        }

        if(strpos($player[8], '=') !== false){
            return "El campo elegibilidad contiene formulas, debe contener un valor numérico.";
        }

    	$newPlayer = new Player;
    	$newPlayer->first_name = $player[0];
    	$newPlayer->middle_name = $player[1];
    	$newPlayer->last_name = $player[2];
    	$newPlayer->age = $player[5];
    	$newPlayer->birthdate = $player[7];
    	$newPlayer->city = $player[10];
    	$newPlayer->elegibility = $player[8];
    	$newPlayer->curp = $player[4];
    	$newPlayer->number = $player[3];
    	$newPlayer->league = $player[12];
    	$newPlayer->phone = $player[14];
    	$newPlayer->email = $player[15];
    	$newPlayer->address = $player[11];
    	$newPlayer->team = $player[13];
    	
    	try{
    		if(Carbon::parse($player[7]) == false){
	    		return "Formato de fecha inválido.";
	    	}
    	}catch(\Exception $e){
    		return "Formato de fecha inválido.";
    	}


    	//category
        $category = Category::where('name',$player[16])->first(); 

        if(!$category){
        	return "Categoría no encontrada.";
        }   

    	$newPlayer->category_id = $category->id;

    	//state
        $state = CatState::where('name',$player[9])->first(); 

        if(!$state){
        	return "Estado no encontrado.";
        }

    	$newPlayer->cat_state_id = $state->id;

    	//dominant_hand
        $dominantHand = CatDominantHand::where('name',$player[18])->first();

        if(!$dominantHand){
        	return "Mano dominante no encontrada.";
        }

        $newPlayer->cat_dominant_hand_id = $dominantHand->id;

        //dominant_hand_bat
        $dominantHandBat = CatDominantHand::where('name',$player[19])->first();

        if(!$dominantHandBat){
        	return "Mano dominante bateo no encontrada.";
        }

        $newPlayer->cat_dominant_hand_bat_id = $dominantHandBat->id;

        $principalPosition = CatPosition::where('name',$player[20])->first();

        if(!$principalPosition){
        	return "Posición principal no encontrada.";
        }

        $newPlayer->secundary_position_id = $principalPosition->id;

        if(!is_null($player[20])){
        	$secundaryPosition = CatPosition::where('name',$player[21])->first();

	        if($secundaryPosition){
	        	$newPlayer->secundary_position_id = $secundaryPosition->id;	
	        }
        }

    	$gender = CatGender::where('name',$player[6])->first();

    	if(!$gender){
    		return "Genero no encontrada.";
    	}

    	$newPlayer->cat_gender_id = $gender->id;

    	$campus = Campus::where('name',$player[17])->first();

    	if(!$campus){
    		return "Sede no encontrada.";
    	}

    	$newPlayer->campus_id = $campus->id;

    	$newPlayer->save();
    	return $newPlayer;
    }

    private function isValidPlayer($player){
    	if( is_null($player[0]) || is_null($player[1]) || is_null($player[5])
    		|| is_null($player[7]) || is_null($player[10]) 
    		|| is_null($player[16]) || is_null($player[9]) 
    		|| is_null($player[17]) || is_null($player[14]) 
    		|| is_null($player[11]) || is_null($player[18])
    		|| is_null($player[19]) || is_null($player[20]) 
    		){
    		return false;
    	}

    	
    	
    	return true;
    }
}
