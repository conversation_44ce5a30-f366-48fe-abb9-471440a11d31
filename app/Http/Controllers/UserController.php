<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\User;
use App\Model\Ticket;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\UserRequest;
use Carbon\Carbon;

use Illuminate\Support\Facades\Log;
use Illuminate\Auth\Events\Registered;
use Illuminate\Validation\Rule;
use DB;
use Illuminate\Support\Facades\URL;

class UserController extends Controller
{

	public function index()
	{
		return User::selectRaw('users.*')
				  ->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				  ->join('roles as r', 'r.id', 'mhr.role_id')
				  ->whereIn('r.name', ['Rifante', 'Comprador'])
				  ->get();
	}

	/**
	 * @OA\Post(
	 *      path="/register",
	 *      tags={"Autenticación"},
	 *      summary="Registro de un cliente",
	 *      description="Registro de un cliente mediante correo electrónico y contraseña",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   required={"email","password","name","last_name","nickname"},
	 *                   @OA\Property(
	 *                       property="password",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="email",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="name",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="last_name",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="nickname",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="phone",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="code_area",
	 *                       type="string",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/UserPermissions")
	 *      )
	 * )
	 */
	public function store(Request $request)
	{
		$loginData = $request->validate([
			'email' => 'email|required|unique:users|max:255',
			'name' => 'required|string|max:255',
			'last_name' => 'nullable|string|max:255',
			'nickname' => 'required|string|unique:users|max:255',
			'password' => 'required_without_all:facebook_id,apple_id|string|max:255',
			'facebook_id' => 'required_without_all:password,apple_id|string|max:255',
			'apple_id' => 'required_without_all:password,facebook_id|string|max:255',
			'phone' => 'nullable|string|max:255',
			'code_area' => 'nullable|string|max:255',
		]);

		$user = new User();
		$user->fill($request->all());

		if ($request->has('password')) {
			$user->password = bcrypt($request->input("password"));
		}

		if ($request->has('facebook_id')) {
			$user->facebook_id = $request->input('facebook_id');
			$user->email_verified_at = Carbon::now();
		}

		if ($request->has('apple_id')) {
			$user->apple_id = $request->input('apple_id');
			$user->email_verified_at = Carbon::now();
		}

		if ($request->has('phone')) {
			$user->phone = $request->input('phone');
		}

		if ($request->has('code_area')) {
			$user->code_area = $request->input('code_area');
		}

		if ($request->has('url_photo')) {
			$user->url_photo = $request->input('url_photo');
		}

		$user->save();

		$role = Role::where('name', 'Comprador')->first();

		if ($role != null) {
			$user->assignRole($role->name);
		}

		if (!$request->has('facebook_id') && !$request->has('apple_id')) {
			event(new Registered($user));
		}

		return $user;
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		$user = User::findOrFail($id);
		$roles = $user->getRoleNames();
		if (count($roles) > 0) {
			$role = Role::where("name", $roles[0])->first();
			$user->role_id = $role->id;
		} else {
			$user->role_id = "";
		}
		return $user;
	}


	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */

	public function update(UserRequest $request, $id)
	{
		$user = User::findOrFail($id);
		$user->name = $request->input("name");

		if ($request->has("active"))
			$user->active = $request->input("active");


		$user->save();
		if ($request->has('role_id')) {
			$role = Role::find($request->input('role_id'));
			$user->syncRoles($role->name);
		}

		return response()->json($user);

	}


	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$user = User::findOrFail($id);
		$user->delete();
		return $user;
	}


	/**
	 * Get permissions from logged user
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function getPermissions(Request $request)
	{
		$user = $request->user();
		$user->permission = $user->getPermissionsViaRoles()->pluck('name')->toArray();

		if ($user->roles && count($user->roles) > 0) {
			$user->role = $user->roles[0]->name;
		}

		unset($user->roles);
		return $user;
	}

	/**
	 * Change password from Admin - No check old password
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function changePasswordByAdmin(Request $request, $id)
	{
		if (!$request->has('password')) {
			return response(400);
		}

		$user = User::findOrFail($id);
		$user->password = bcrypt($request->input('password'));
		$user->save();
		return $user;
	}

	/**
     * @OA\Post(
     *      path="/user/change-password",
     *      tags={"Autenticación"},
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     * 					 required={"new_password","old_password"},
     *                   @OA\Property(
     *                       property="old_password",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="new_password",
     *                       type="string",
     *                   ),
     *               ),
     *           ),
     *      ),
     *      summary="Actualizar información de un usuario",
     *      description="Actualizar información de un usuario",
     *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/User")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function changePasswordByUser(Request $request)
    {
        $loginData = $request->validate([
			'new_password' => 'required|string|max:255',
			'old_password' => 'required|string|max:255',

		]);

        $user = $request->user();

        if(!Hash::check($request->input('old_password'),$user->password) ){
            $returnData = array(
                'status' => 'error',
                'message' => 'Wrong password'
            );
            return response($returnData,412)->header('Content-Type', 'json');
        }

        $user->password = bcrypt($request->input('new_password'));
        $user->save();
        return $user;
    }

	public function syncPermissionEvents($events, $typeTests, $userId)
	{
		$eventDeleted = UserEvent::where('user_id', $userId)->delete();
		$testDeleted = UserTestType::where('user_id', $userId)->delete();
		$eventArray = [];
		$tests = [];


		foreach ($events as $event) {
			$inserted = new UserEvent;
			$inserted->user_id = $userId;
			$inserted->event_id = $event;

			$inserted->save();

			$eventArray[] = $inserted;
		}

		foreach ($typeTests as $test) {
			$inserted = new UserTestType;
			$inserted->user_id = $userId;
			$inserted->type_test_id = $test;

			$inserted->save();

			$tests[] = $inserted;
		}

		return ["events" => $events, "test" => $tests];
	}

	/**
	 * @OA\Get(
	 *      path="/getResend/{id}",
	 *      tags={"Verify"},
	 *      summary="Obtener URL para reenviar el correo",
	 *      description="Genera la URL con la firma para reenviar el correo de verificación.",
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id del usuario",
	 *         required=true,
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="string"
	 *          )
	 *      )
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function getResend($id)
	{
		$user = User::findOrFail($id);
		$url = URL::signedRoute('verification.resend', ['id' => $user->id]);
		return response(['urlResend' => $url], 200);
	}


	public function checkMail(Request $request)
	{
		$loginData = $request->validate([
			'email' => 'string|required|max:255'
		]);

		$user = User::where('email', $request->input('email'))->first();

		return ['valid' => $user == null ? true : false];
	}


	public function checkNickname(Request $request)
	{
		$loginData = $request->validate([
			'nickname' => 'string|required|max:255'
		]);

		$user = User::where('nickname', $request->input('nickname'))->first();

		return ['valid' => $user == null ? true : false];
	}


	/**
	 * @OA\Put(
	 *      path="/user/lock-time",
	 *      tags={"Boletos"},
	 *      summary="Asignar el tiempo para el bloqueo de boletos.",
	 *      description="Asignar el tiempo para el bloqueo de boletos. Si se envia el Id de la rifa se actualizará el tiempo de bloqueo para los boletos de la rifa.",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="raffle_id",
	 *                       type="number",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/User")
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function updateLockTime(Request $request)
    {
		$user = $request->user();

		$time = Carbon::now()->addMinutes(10);

		try {

			if($request->has('raffle_id')){
				$time = Carbon::now()->addMinutes(5);
				$raffle = $request->input('raffle_id');
				$tickets = Ticket::where('status','Locked')
					->where('buyer_id',$user->id)
					->where('raffle_id',$raffle)
					->update(
						[
							"locked_finish" => $time
						]);
			}

            $user->ticket_locked_finish = $time;
			$user->save();

            DB::commit();
        } catch (\PDOException $e) {
            DB::rollBack();
            return  $e;
        }


		return $user;
	}

	/**
	 * @OA\Put(
	 *      path="/user/unlock-time",
	 *      tags={"Boletos"},
	 *      summary="Liberar boletos.",
	 *      description="Liberar boletos. Se liberan los boletos que tiene apartado el usuario. Si no se llama el servicio el boleto estará bloqueado hasta que el proceso automatico los detecte.",
	 *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/User")
     *      ),
     *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function cancelLockTime(Request $request)
    {
		$user = $request->user();

		try {
			$tickets = Ticket::where('status','Locked')
					->where('buyer_id',$user->id)
					->update(
						[
							"locked" => false,
							"locked_finish" => null,
							"buyer_id" => null,
							"status" => "Free",
						]);

            $user->ticket_locked_finish = null;
			$user->save();

            DB::commit();
        } catch (\PDOException $e) {
            DB::rollBack();
            return  $e;
        }


		return $user;
	}

	/**
     * @OA\Post(
     *      path="/profile",
     *      tags={"Perfil"},
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="multipart/form-data",
     *               @OA\Schema(
     *                   @OA\Property(
     *                       property="name",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="last_name",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="nickname",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="phone",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="code_area",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="file",
     *                       type="string",
     *                       format="binary",
     *                   ),
     *               ),
     *           ),
     *      ),
     *      summary="Actualizar información de un usuario",
     *      description="Actualizar información de un usuario",
     *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/User")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
	public function profile(Request $request)
	{
		$user = $request->user();

		$data = $request->validate([
			'name' => 'string|nullable|max:255',
			'last_name' => 'string|nullable|max:255',
			'nickname' => [
					'string',
					'nullable',
					'max:255',
					Rule::unique('users','nickname')->ignore($user->id,'id'),
				],
			'phone' => 'string|nullable|max:20',
			'code_area' => 'string|nullable|max:20',
			'file' => 'nullable|image',
		]);

		$user = User::findOrFail($user->id);

		if($request->has("name")){
			$user->name = $request->input("name");
		}

		if($request->has("last_name")){
			$user->last_name = $request->input("last_name");
		}

		if($request->has("code_area")){
			$user->code_area = $request->input("code_area");
		}

		if($request->has("phone")){
			$user->phone = $request->input("phone");
		}

		if($request->has("nickname")){
			$user->nickname = $request->input("nickname");
		}

		$url = "";
        if(request()->hasFile('file')){
            $url = $this->uploadFile(
                    $request->file('file'),
                    Carbon::now()->timestamp.'.'.request()->file('file')->getClientOriginalExtension(),
                    'rafiki/user-photo');

            $user->url_photo = $url;
        }

		$user->save();

		return response()->json($user);
	}


	/**
     * @OA\Get(
     *      path="/profile",
     *      tags={"Perfil"},
     *      summary="Obtener la información del perfil",
     *      description="Obtener la información del perfil",
     *      @OA\Response(
     *           response="200",
     *           description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/User")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
	public function getProfile(Request $request)
	{
		$user = User::findOrFail($request->user()->id);

		return $user;
	}
}
