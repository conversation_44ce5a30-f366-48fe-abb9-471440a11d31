<?php

namespace App\Http\Controllers;

use App\Model\Subcategory;

use Illuminate\Http\Request;

class SubcategoryController extends Controller
{
	/**
	 * Display a listing of the resource.
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function index()
	{
		return Subcategory::all();
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$subcategory = new Subcategory();
		$subcategory->fill($request->all());
		$subcategory->save();

		return $subcategory;
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return Subcategory::findOrfail($id);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, $id)
	{
		$subcategory = Subcategory::findOrfail($id);
		$subcategory->fill($request->all());
		$subcategory->save();

		return $subcategory;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$subcategory = Subcategory::findOrfail($id);
		return response()->json($subcategory->delete());
	}
}
