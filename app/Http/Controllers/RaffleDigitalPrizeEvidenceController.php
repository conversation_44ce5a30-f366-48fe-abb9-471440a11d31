<?php

namespace App\Http\Controllers;

use App\Model\Raffle;
use App\Model\RaffleDigitalPrizeEvidence;
use App\Http\Requests\RaffleDigitalPrizeEvidenceAdd as RaffleDigitalPrizeEvidenceAddRequest;

class RaffleDigitalPrizeEvidenceController extends Controller
{
	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle/{raffle}/digital-prize-evidence",
	 *      tags={"Rifa","Digital","Premio","Evidencias"},
	 *      @OA\Parameter(
	 *         name="raffle",
	 *         in="path",
	 *         description="Id de la tabla raffles",
	 * 		   example="external | instructions | file_instructions | redeem_code",
	 *         required=true,
	 *      ),
	 *      summary="Agregar evidencia del premio digital",
	 *      description="Agregar evidencia del premio digital",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleDigitalPrizeEvidenceAdd"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleDigitalPrizeEvidence")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function addEvidence(RaffleDigitalPrizeEvidenceAddRequest $request, Raffle $raffle)
	{
		$raffleDigitalPrizeEvidence = RaffleDigitalPrizeEvidence::create([
			'raffle_id' => $raffle->id,
			'user_id'   => $request->user()->id,
			'type'      => $request->type
		]);

		return $raffleDigitalPrizeEvidence;
	}
}
