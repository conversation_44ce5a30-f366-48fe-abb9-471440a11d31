<?php

namespace App\Http\Controllers;

use App\Model\RaffleWinner;
use App\Http\Requests\RaffleWinnerUpdateAddress as RaffleWinnerUpdateAddressRequest;
use App\Http\Requests\RaffleWinnerUpdateDeliveryInformation as RaffleWinnerUpdateDeliveryInformationRequest;
use App\Http\Requests\RaffleWinnerUpdatePrizeStatus as RaffleWinnerUpdatePrizeStatusRequest;
use Carbon\Carbon;

class RaffleWinnerController extends Controller
{
	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle-winner/{id}/update-address",
	 *      tags={"Ganador,Rifa"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la tabla raffle_winners",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar dirección del ganador de la rifa",
	 *      description="Actualizar dirección del ganador de la rifa",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleWinnerUpdateAddress"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleWinner")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function updateAddress(RaffleWinnerUpdateAddressRequest $request, RaffleWinner $raffleWinner)
	{
		$raffleWinner->address_id = $request->address_id;
		$raffleWinner->save();

		return $raffleWinner;
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle-winner/{id}/update-delivery-information",
	 *      tags={"Ganador,Rifa"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la tabla raffle_winners",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar la información de entrega del premio",
	 *      description="Actualizar la información de entrega del premio",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleWinnerUpdateDeliveryInformation"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleWinner")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function updateDeliveryInformation(RaffleWinnerUpdateDeliveryInformationRequest $request, RaffleWinner $raffleWinner)
	{
		$raffleWinner->delivery_company        = $request->delivery_company;
		$raffleWinner->track_id                = $request->track_id;
		$raffleWinner->estimated_delivery_date = $request->estimated_delivery_date;
		$raffleWinner->track_url = $request->track_url;

		$url = '';
		if (request()->hasFile('delivery_voucher_file')) {
			$url = $this->uploadFile(
				$request->file('delivery_voucher_file'),
				Carbon::now()->timestamp . request()->file('delivery_voucher_file')->getClientOriginalName(),
				'rafiki/raffle_winners'
			);

			$raffleWinner->delivery_voucher_url = $url;
		}

		$raffleWinner->save();

		return $raffleWinner;
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle-winner/{id}/update-prize-status",
	 *      tags={"Ganador,Rifa"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la tabla raffle_winners",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar el estatus del premio",
	 *      description="Actualizar el estatus del premio",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleWinnerUpdatePrizeStatus"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleWinner")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function updatePrizeStatus(RaffleWinnerUpdatePrizeStatusRequest $request, RaffleWinner $raffleWinner)
	{
		$raffleWinner->received_prize        = $request->received_prize;
		$raffleWinner->reception_date        = $request->reception_date;

		$url = '';
		if (request()->hasFile('evidence_file')) {
			$url = $this->uploadFile(
				$request->file('evidence_file'),
				Carbon::now()->timestamp . request()->file('evidence_file')->getClientOriginalName(),
				'rafiki/raffle_winners'
			);

			$raffleWinner->evidence_url = $url;
		}

		$raffleWinner->save();

		return $raffleWinner;
	}
}
