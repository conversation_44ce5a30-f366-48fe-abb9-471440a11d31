<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Model\RafflerPayment;
use App\Model\Raffle;
use Carbon\Carbon;
use App\Http\Requests\RafflerPaymentUploadEvidence as RafflerPaymentUploadEvidenceRequest;
use App\Http\Requests\RafflerPaymentUpdate as RafflerPaymentUpdateRequest;

class RafflerPaymentController extends Controller
{
	public function index(Request $request)
	{
		$orderColumns = ['status', 'created_at'];
		$orderWays = ['asc', 'desc'];

		$page = $request->has('page') ? $request->input('page') : 1;
		$size = $request->has('size') ? $request->input('size') : 20;

		$order = $request->has('order') ? $request->input('order') : 'created_at';
		$orderWay = $request->has('order-way') ? $request->input('order-way') : 'desc';

		if ($order != null) {
			$order = (in_array($order, $orderColumns)) ? $order : null;
		}

		if ($orderWay != null) {
			$orderWay = (in_array($orderWay, $orderWays)) ? $orderWay : null;
		}

		return RafflerPayment::select('*')
			->orderByRaw("case when status = 'pending' then 1 when status = 'accepted' then 2 else 3 end")
			->orderBy($order, $orderWay)
			->paginate($size, $page);
	}

	public function update(RafflerPaymentUpdateRequest $request, $id)
	{
		$rafflerPayment = RafflerPayment::findOrFail($id);

		$rafflerPayment->fill($request->all());
		$rafflerPayment->save();

		return $rafflerPayment;
	}

	public function generatePayment(Request $request, RafflerPayment $rafflerPayment)
	{
		if (!$rafflerPayment->can_generate_payment) {
			abort(422, 'The payment could not be generated.');
		}

		$raffle = Raffle::findOrFail($rafflerPayment->raffle_id);

		$newPayment = $raffle->create_payment();

		return $newPayment;
	}

	public function show($id)
	{
		$payment = RafflerPayment::with(['raffler','application'])->where('id',$id)->first();
		return $payment;
	}

	public function uploadEvidence(RafflerPaymentUploadEvidenceRequest $request, RafflerPayment $rafflerPayment)
	{
		if (request()->hasFile('file')) {
			$url = $this->uploadFile(
				$request->file('file'),
				Carbon::now()->timestamp . request()->file('file')->getClientOriginalName(),
				'rafiki/raffler_payments'
			);

			$rafflerPayment->evidence_url = $url;
		}

		$rafflerPayment->save();

		return $rafflerPayment;
	}
}
