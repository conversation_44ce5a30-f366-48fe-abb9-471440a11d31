<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\Address;

class AddressController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @OA\Get(
     *      path="/address",
     *      tags={"Direcciones"},
     *      summary="Listado de direcciones del usuario",
     *      description="Listado de direcciones del usuario",
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Address")
     *          )
     *      ),
     * )
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request){
        $user = $request->user();

        return Address::where('user_id', $user->id)->active()->get();
    }


    /**
     * @OA\Get(
     *      path="/address/{id}",
     *      tags={"Direcciones"},
     *      summary="Obtener los datos de una dirección",
     *      description="Obtener los datos de una dirección",
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la dirección",
     *         required=true,
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Address")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id){
        return Address::findOrFail($id);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @OA\Post(
     *      path="/address",
     *      tags={"Direcciones"},
     *      summary="Guardar una rifa",
     *      description="Guardar una rifa",
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   ref="#/components/schemas/AddressSave"
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Address")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request){
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'cp' => 'required|string|max:5',
            'code_area' => 'required|string|max:5',
            'phone' => 'required|string|max:20',
            'contact_name' => 'required|string|max:255'
        ]);

        $address = new Address;
        $address->fill($request->all());
        $address->user_id = $request->user()->id;
        $address->active = 1;

        $address->save();

        return $address;
    }


    /**
     * 
     *
     * @OA\Put(
     *      path="/address/{id}",
     *      tags={"Direcciones"},
     *      summary="Guardar una rifa",
     *      description="Guardar una rifa",
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la dirección",
     *         required=true,
     *      ),
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   ref="#/components/schemas/AddressSave"
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Address")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id){
        $data = $request->validate([
            'name' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'cp' => 'required|string|max:5',
            'code_area' => 'required|string|max:5',
            'phone' => 'required|string|max:20',
            'contact_name' => 'required|string|max:255'
        ]);

        $address = Address::findOrFail($id);
        $address->fill($request->all());

        $address->save();

        return $address;
    }


    /**
     * 
     *
     * @OA\Delete(
     *      path="/address/{id}",
     *      tags={"Direcciones"},
     *      summary="Guardar una rifa",
     *      description="Guardar una rifa",
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la dirección",
     *         required=true,
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(ref="#/components/schemas/Address")
     *      ),
     *      security={{"bearerAuth":{}}}
     * )
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy($id){
        $address = Address::findOrFail($id);

        abort_unless(auth()->user()->id === $address->user_id, 403);

        $address->active = false;
        $address->save();

        return $address;
    }
}
