<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Request;

/**
 * @OA\Info(title="Ra<PERSON><PERSON>", version="0.1")
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    protected function uploadFile($file,$fileName,$folder, $disk = 'spaces'){
        $directories = Storage::disk($disk)->directories();
        if(!in_array($folder, $directories)){
            Storage::disk($disk)->makeDirectory($folder);   
        }

        Storage::disk($disk)->putFileAs(
            $folder,$file,$fileName
        );
        Storage::disk($disk)->setVisibility($folder.'/'.$fileName, 'public');
        return Storage::disk($disk)->url($folder.'/'.$fileName);
    }

    protected function downloadFile(Request $request){
    	$url = $request->input('url');
    	if( strpos($url, '.com/') === false ){
    		return abort(403,'Archivo no encontrado');
    	}

    	$driver = env('FILESYSTEM_DRIVER');
    	$filePath = explode( ".com/" , $url );
    	$fileNameArray = explode( "/" , $filePath[1] );
    	$fileName = $fileNameArray[count($fileNameArray)-1];
    	$file = Storage::disk($driver)->get($filePath[1]);
    	$mimetype = \GuzzleHttp\Psr7\mimetype_from_filename($filePath[1]);
    	$headers = [
	        'Content-Type' => $mimetype,
	        'Content-Disposition' => 'attachment; filename="'. $fileName .'"',
	    ];
    	
    	return response($file, 200, $headers); 
    }

    protected function deleteFile($fileName, $disk = 'spaces'){
        $name = explode( ".com/" , $fileName )[1];

        $exists = Storage::disk($disk)->exists($name);

        $deleted = false;

        if($exists){
            $deleted = Storage::disk($disk)->delete($name);
        }
        
        return response()->json($deleted);
    }
}
