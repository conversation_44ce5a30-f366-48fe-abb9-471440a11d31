<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Model\CatState;

class CatalogController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @OA\Get(
     *      path="/catalog/state",
     *      tags={"Catalogos"},
     *      summary="Listado de estados",
     *      description="Listado de estados",
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Catalog")
     *          )
     *      ),
     * )
     *
     * @return \Illuminate\Http\Response
     */
    public function states()
    {
        return CatState::all();
    }
}
