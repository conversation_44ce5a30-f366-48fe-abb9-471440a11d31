<?php

namespace App\Http\Controllers;

use App\Model\Balance;
use Illuminate\Http\Request;
use Carbon\Carbon;

class RefundController extends Controller
{
	public function index()
	{
		return Balance::whereNotNull('raffle_id')
					  ->orderBy('created_at')
					  ->get();
	}

	public function store(Request $request)
	{
	}

	public function show($id)
	{
		return Balance::findOrfail($id);
	}

	public function update(Request $request, $id)
	{
	}

	public function destroy($id)
	{
	}
}