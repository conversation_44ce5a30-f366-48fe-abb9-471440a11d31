<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Controllers\UserController;
use Illuminate\Http\Request;
use App\User;
use App\Model\Device;

use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Hash;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
	/**
	 * @OA\Post(
	 *      path="/login",
	 *      tags={"Autenticación"},
	 *      summary="Inicio de sesión",
	 *      description="Inicio de sesión, los campos requeridos varian dependiendo del metodo para iniciar sesión, si se quiere ingresar por password es necesario ingresar con 'email' y 'password'. Si se va a iniciar con facebook es necesario enviar 'email', 'facebook_id', 'name' y 'last_name', en este caso si el usuario no esta registrado se hará el registro en automatico.",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="password",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="email",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="facebook_id",
	 *                       type="string",
	 *                       description="Login con facebook.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="apple_id",
	 *                       type="string",
	 *                       description="Login con apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="url_image",
	 *                       type="string",
	 *                       description="Login con facebook / apple. Imagen que se obtiene desde la información de facebook / apple, solo se usará si el usuario no estaba previamente registrado",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="name",
	 *                       type="string",
	 *                       description="Login con facebook / apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="last_name",
	 *                       type="string",
	 *                       description="Login con facebook / apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="token",
	 *                       type="string",
	 *                       description="Login desde moviles.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="type",
	 *                       type="string",
	 *                       example="ios:android",
	 *                       description="Login desde moviles.",
	 *                   ),

	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/UserPermissions")
	 *      )
	 * )
	 */
	public function login(Request $request)
	{
		return $this->_validUserCredentials($request, ['Rifante', 'Comprador']);
	}

	/**
	 * @OA\Post(
	 *      path="/admin/login",
	 *      tags={"Autenticación"},
	 *      summary="Inicio de sesión",
	 *      description="Inicio de sesión, los campos requeridos varian dependiendo del metodo para iniciar sesión, si se quiere ingresar por password es necesario ingresar con 'email' y 'password'. Si se va a iniciar con facebook es necesario enviar 'email', 'facebook_id', 'name' y 'last_name', en este caso si el usuario no esta registrado se hará el registro en automatico.",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="password",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="email",
	 *                       type="string",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="facebook_id",
	 *                       type="string",
	 *                       description="Login con facebook.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="apple_id",
	 *                       type="string",
	 *                       description="Login con apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="url_photo",
	 *                       type="string",
	 *                       description="Login con facebook / apple. Imagen que se obtiene desde la información de facebook / apple, solo se usará si el usuario no estaba previamente registrado",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="name",
	 *                       type="string",
	 *                       description="Login con facebook / apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="last_name",
	 *                       type="string",
	 *                       description="Login con facebook / apple.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="token",
	 *                       type="string",
	 *                       description="Login desde moviles.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="type",
	 *                       type="string",
	 *                       example="ios:android",
	 *                       description="Login desde moviles.",
	 *                   ),
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/UserPermissions")
	 *      )
	 * )
	 */
	public function loginAdmin(Request $request)
	{
		return $this->_validUserCredentials($request, ['Administrador']);
	}

	/**
	 * Valid user credentials by role
	 */
	private function _validUserCredentials($request, $roles)
	{
		$loginData = $request->validate([
			'email' => 'email|required',
			'name' => 'required_without:password|string',
			'last_name' => 'required_without:password|string',
			'nickname' => 'nullable|string',
			'password' => 'required_without_all:facebook_id,apple_id|string',
			'facebook_id' => 'required_without_all:password,apple_id|string',
			'apple_id' => 'required_without_all:password,facebook_id|string',
		]);

		//login con usuario y password
		if ($request->has('password') && $request->input('password') != null) {
			$user = User::selectRaw('users.*')
				->where("email", $request->input('email'))
				->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				->join('roles as r', 'r.id', 'mhr.role_id')
				->whereIn('r.name', $roles)
				->where('active', '1')
				->first();

			if ($user == null) {
				return response(['message' => 'Invalid Credentials', 'status' => 401], 401);
			}

			if (!(Hash::check($request->get('password'), $user->getAuthPassword()))) {
				return response(['message' => 'Invalid Credentials', 'status' => 401], 401);
			}
		}

		//login con facebook
		if ($request->has('facebook_id') && $request->input('facebook_id') != null) {
			$user = User::selectRaw('users.*')
				->where("facebook_id", $request->input('facebook_id'))
				->where("email", $request->input('email'))
				->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				->join('roles as r', 'r.id', 'mhr.role_id')
				->whereIn('r.name', $roles)
				->where('active', '1')
				->first();

			if ($user == null && $request->has('nickname')) {
				$client = new UserController;
				$user = $client->store($request);
			} else if($user == null && !$request->has('nickname')) {
				$pos = strpos($request->input('email'), '@');
				$nickname = substr($request->input('email'), 0, $pos);
				$nickname = $nickname . time();

				$request->merge([
					'nickname' => $nickname,
				]);

				$client = new UserController;
				$user = $client->store($request);
			}
		}

		//login con apple_id
		if ($request->has('apple_id') && $request->input('apple_id') != null) {
			$user = User::selectRaw('users.*')
				->where("apple_id", $request->input('apple_id'))
				->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				->join('roles as r', 'r.id', 'mhr.role_id')
				->whereIn('r.name', $roles)
				->where('active', '1')
				->first();

			if ($user == null && $request->has('nickname')) {
				$client = new UserController;
				$user = $client->store($request);
			} else if($user == null && !$request->has('nickname')){
				$pos = strpos($request->input('email'), '@');
				$nickname = substr($request->input('email'), 0, $pos);
				$nickname = $nickname;

				$request->merge([
					'nickname' => $nickname . time(),
				]);

				$client = new UserController;
				$user = $client->store($request);
			}
		}

		if ($user->email_verified_at == null) {
			$url = URL::signedRoute('verification.resend', ['id' => $user->id]);
			return response(
				[
					'message' => 'User not validated',
					'status' => 400,
					'urlResend' => $url
				],
				200
			);
		}

		if (
			$request->has('token') &&
			($request->has('type') && ($request->input('type') == 'ios' || $request->input('type') == 'android'))
		) {
			$device = Device::where(
				[
					'user_id' => $user->id,
					'token' => $request->input('token'),
					'type' => $request->input('type'),
				]
			)
				->first();

			//si no existe lo guardamos
			if ($device == null) {
				$device = new Device;
				$device->token = $request->input('token');
				$device->type = $request->input('type');
				$device->user_id = $user->id;
				$device->save();
			}
		}

		$accessToken = $user->createToken('authToken')->accessToken;

		if ($user->roles && count($user->roles) > 0) {
			$user->role = $user->roles[0]->name;
		}

		$user->append('rafiki');

		$user->permission = $user->getPermissionsViaRoles()->pluck('name')->toArray();

		return response(['user' => $user, 'access_token' => $accessToken]);
	}

	/**
	 * @OA\Post(
	 *      path="/app/logout",
	 *      tags={"Autenticación"},
	 *      summary="Cierre de sesión",
	 *      description="Cierre de sesión, se puede incluir los datos del dispositivo.",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   @OA\Property(
	 *                       property="token",
	 *                       type="string",
	 *                       description="Login desde moviles.",
	 *                   ),
	 *                   @OA\Property(
	 *                       property="type",
	 *                       type="string",
	 *                       example="ios:android",
	 *                       description="Login desde moviles.",
	 *                   ),

	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200",
	 *           description="Exito"
	 *      )
	 * )
	 */
	public function logout(Request $request){

		$user = $request->user();

		if (
			$request->has('token') &&
			($request->has('type') && ($request->input('type') == 'ios' || $request->input('type') == 'android'))
		) {

			$device = Device::where(
				[
					'user_id' => $user->id,
					'token' => $request->input('token'),
					'type' => $request->input('type'),
				]
			)
				->first();

			if($device != null){
				$device->delete();
			}
		}

		Auth::user()->token()->revoke();

		return [
			"message" => "true"
		];
	}
}
