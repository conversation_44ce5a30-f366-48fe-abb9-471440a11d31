<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Illuminate\Http\Request;

use App\User;
use Illuminate\Auth\Events\Verified;
use App\Exceptions\AuthorizationException;

use Illuminate\Support\Facades\Log;


class VerificationController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Email Verification Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling email verification for any
    | user that recently registered with the application. Emails may also
    | be re-sent if the user didn't receive the original email message.
    |
    */

    use VerifiesEmails;

    /**
     * Where to redirect users after verification.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        
    }


    /**
     * @OA\Get(
     *      path="/email/verify/{id}/{hash}",
     *      tags={"Verify"},
     *      summary="Verifica un usuario",
     *      description="Verifica al usuario con la información que se envió a su correo.",
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id del usuario",
     *         required=true,
     *      ),
     *      @OA\Parameter(
     *         name="hash",
     *         in="path",
     *         description="Cadena para verificar el usuario",
     *         required=true,
     *      ),
     *     @OA\Parameter(
     *         name="expires",
     *         in="query",
     *         description="Timestap con la fecha en que expira la url",
     *         required=true,
     *      ),
     *      @OA\Parameter(
     *         name="signature",
     *         in="query",
     *         description="Firma generada con la información del usuario",
     *         required=true,
     *      ),
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="boolean"
     *          )
     *      )
     * )
     *
     * @return \Illuminate\Http\Response
     */
    public function verify(Request $request)
    {
        $user = User::find($request->route('id'));

        if (!$request->hasValidSignature() || !hash_equals((string) $request->route('hash'), sha1($user->getEmailForVerification()))) {
            return "false";
        }

        if ($user->markEmailAsVerified())
            event(new Verified($user));

        return "true";
    }


    /**
     * @OA\Post(
     *      path="/email/resend",
     *      tags={"Verify"},
     *      summary="Envía un correo de verificación",
     *      description="Envía un correo de verificación.",
     *     @OA\Parameter(
     *         name="id",
     *         in="query",
     *         description="Id del usuario",
     *         required=true,
     *      ),
     *      @OA\Parameter(
     *         name="signature",
     *         in="query",
     *         description="Firma generada con la información del usuario",
     *         required=true,
     *      ),
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="boolean"
     *          )
     *      )
     * )
     *
     * @return \Illuminate\Http\Response
     */
    public function resend(Request $request)
    {   
        $user = User::findOrFail($request->input('id'));

        if ($request->input('id') && $request->hasValidSignature()) {
            
        }

        //Log::debug($request->input('id'));

        $user = $user ?: $request->user();

        $user->sendEmailVerificationNotification();

        return 'true';
    }

}
