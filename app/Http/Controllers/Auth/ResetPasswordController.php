<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Support\Facades\Password;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     *
     * @OA\Post(
     *      path="/reset-password",
     *      tags={"Autenticación"},
     *      summary="Envía un correo al usuario para que pueda recuperar su contraseña.",
     *      description="Envía un correo al usuario para que pueda recuperar su contraseña.",
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   required={"email"},
     *                   @OA\Property(
     *                       property="email",
     *                       type="string",
     *                   ),
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(type="boolean")
     *      )
     * )
     */
    public function reset(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
        ? 'true'
        : 'false';
    }

    /**
     *
     * @OA\Post(
     *      path="/reset-confirm",
     *      tags={"Autenticación"},
     *      summary="Segundo paso de la recuperación de contraseña.",
     *      description="Segundo paso de la recuperación de contraseña.",
     *      @OA\RequestBody(
     *          required=true,
     *           @OA\MediaType(
     *               mediaType="application/json",
     *               @OA\Schema(
     *                   required={"email","token","password","password_confirmation"},
     *                   @OA\Property(
     *                       property="email",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="token",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="password",
     *                       type="string",
     *                   ),
     *                   @OA\Property(
     *                       property="password_confirmation",
     *                       type="string",
     *                   ),
     *               ),
     *           ),
     *      ),
     *      @OA\Response(
     *           response="200", description="Exito",
     *           @OA\JsonContent(type="boolean")
     *      )
     * )
     */
    public function resetConfirm(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->save();

                $user->setRememberToken(Str::random(60));

                event(new PasswordReset($user));
            }
        );
        
        return $status == Password::PASSWORD_RESET
        ? "true"
        : "false";
    }
}
