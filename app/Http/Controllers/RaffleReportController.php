<?php

namespace App\Http\Controllers;

use App\Model\RaffleReport;
use Illuminate\Http\Request;
use App\Http\Requests\AddRaffleReport as AddRaffleReportRequest;
use App\Http\Requests\EditRaffleReport as EditRaffleReportRequest;

class RaffleReportController extends Controller
{
	/**
	 * @OA\Get(
	 *      path="/raffle-report",
	 *      tags={"Reporte"},
	 *      summary="Listado de reportes de rifas",
	 *      description="Listado de reportes de rifas",
	 *      @OA\Response(
	 *           response="200", 
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="array",
	 *              @OA\Items(ref="#/components/schemas/RaffleReport")
	 *          )
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 */
	public function index()
	{
		return RaffleReport::orderBy('created_at', 'desc')->get();
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle-report",
	 *      tags={"Reporte"},
	 *      summary="Guardar un reporte",
	 *      description="Guardar un reporte",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleReportSave"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleReport")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(AddRaffleReportRequest $request)
	{
		$raffleReport = new RaffleReport();

		$raffleReport->fill([
			'raffle_id' => $request->raffle_id,
			'comments'  => $request->comments,
			'user_id'   => $request->user()->id
		]);

		$raffleReport->save();

		return $raffleReport;
	}

	/**
	 * Display the specified resource.
	 *
	 *
	 * @OA\Get(
	 *      path="/raffle-report/{id}",
	 *      tags={"Reporte"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id del reporte",
	 *         required=true,
	 *      ),
	 *      summary="Obtener información del reporte",
	 *      description="Obtener información del reporte",
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleReport")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return RaffleReport::findOrfail($id);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Put(
	 *      path="/raffle-report/{id}",
	 *      tags={"Reporte"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id del reporte",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar reporte",
	 *      description="Actualizar reporte",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleReportEdit"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleReport")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(EditRaffleReportRequest $request, $id)
	{
		$raffleReport = RaffleReport::findOrFail($id);

		$data = [
			'admin_id'       => $request->user()->id,
			'status'         => $request->status,
			'admin_comments' => $request->admin_comments,
		];

		$raffleReport->fill($data);
		$raffleReport->save();

		return $raffleReport;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$raffleReport = RaffleReport::findOrFail($id);
		return response()->json($raffleReport->delete());
	}
}
