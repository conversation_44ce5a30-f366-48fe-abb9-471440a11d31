<?php

namespace App\Http\Controllers;

use App\Model\Application;
use App\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Http\Requests\EditApplication as EditApplicationRequest;
use App\Http\Requests\AddApplication as AddApplicationRequest;
use App\Http\Requests\EditApplicationRFC;
use Spatie\Permission\Models\Role;

use Illuminate\Support\Facades\Mail;
use App\Mail\AddApplication;
use App\Mail\CancelApplication;
use App\Mail\AcceptApplication;

use App\Model\Notification;
use App\Helpers\AppHelper;

use Illuminate\Support\Facades\Log;


class ApplicationController extends Controller
{
	/**
	 * @OA\Get(
	 *      path="/application",
	 *      tags={"Solicitudes"},
	 *      summary="Listado de solicitudes",
	 *      description="Listado de solicitudes",
	 *      @OA\Response(
	 *           response="200", 
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="array",
	 *              @OA\Items(ref="#/components/schemas/Application")
	 *          )
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 */
	public function index()
	{
		return Application::orderBy('approval')->get();
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @OA\Post(
	 *      path="/application",
	 *      tags={"Solicitudes"},
	 *      summary="Guardar una solicitud",
	 *      description="Guardar una solicitud",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="multipart/form-data",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/ApplicationSave"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Application")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(AddApplicationRequest $request)
	{
		$application = Application::where('user_id', $request->user()->id)->first();

		if (!$application) {
			$application = new Application();
			$application->user_id = $request->user()->id;
		}

		$files = [
			'url_id_front'         => 'id_front_file',
			'url_id_back'          => 'id_back_file',
			'url_bank_information' => 'bank_information_file',
			'url_rfc_documentation' => 'url_rfc_documentation',
		];

		foreach($files as $fieldName => $fileName) {
			$url = '';
			if (request()->hasFile($fileName)) {
				$url = $this->uploadFile(
					$request->file($fileName),
					Carbon::now()->timestamp . request()->file($fileName)->getClientOriginalName(),
					'rafiki/applications/documents'
				);
	
				$application->{$fieldName} = $url;

				$application->approval = null;
				$application->rejection_reason = null;
				$application->approval_date = null;
			}
		}

		

		if($request->has('rfc')){
			$application->rfc = $request->input('rfc');
		}

		$application->save();

		try{
			$admins = User::selectRaw('users.*')
				   ->join('model_has_roles as mhr', 'mhr.model_id', 'users.id')
				   ->join('roles as r', 'r.id', 'mhr.role_id')
				   ->where('r.name', 'Administrador')
				   ->get();

			foreach($admins as $admin){
				Mail::to($admin->email)
				->send(
					new AddApplication($request->user(), $application)
				);
			}
			
		}catch(\Exception $e){
			Log::debug($e->getMessage());
		}
		

		return $application;
	}

	/**
	 * Display the specified resource.
	 *
	 *
	 * @OA\Get(
	 *      path="/application/{id}",
	 *      tags={"Solicitudes"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la solicitud",
	 *         required=true,
	 *      ),
	 *      summary="Obtener la información de la solicitud",
	 *      description="Obtener la información de la solicitud",
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Application")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return Application::findOrfail($id);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Put(
	 *      path="/application/{id}",
	 *      tags={"Solicitudes"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la solicitud",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar solicitud",
	 *      description="Actualizar solicitud",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/ApplicationEdit"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Application")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(EditApplicationRequest $request, $id)
	{
		$application = Application::findOrFail($id);
		$data = $request->all();
		$data['admin_id'] = $request->user()->id;



		if ($data['approval']) {
			$data['approval_date'] = date('Y-m-d H:i:s');
			$addRole = Role::where('name', 'Rifante')->first();
			$removeRole = Role::where('name', 'Comprador')->first();

			if ($addRole != null && $removeRole != null) {
				$user = $application->user;
				$user->removeRole($removeRole->name);
				$user->assignRole($addRole->name);
			}
		} else {
			$addRole = Role::where('name', 'Comprador')->first();
			$removeRole = Role::where('name', 'Rifante')->first();

			if ($addRole != null && $removeRole != null) {

				$user = $application->user;
				$user->removeRole($removeRole->name);
				$user->assignRole($addRole->name);
			}
		}



		$application->fill($data);

		$application->save();

		return $application;
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/application/{id}/rfc",
	 *      tags={"Solicitudes"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la solicitud",
	 *         required=true,
	 *      ),
	 *      summary="Actualizar solicitud",
	 *      description="Actualizar solicitud",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/ApplicationEditRFC"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/Application")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function updateRFC(EditApplicationRFC $request, $id)
	{
		$application = Application::findOrFail($id);

		$url = $this->uploadFile(
			$request->file('url_rfc_documentation'),
			Carbon::now()->timestamp . request()->file('url_rfc_documentation')->getClientOriginalName(),
			'rafiki/applications/documents'
		);

		$application->url_rfc_documentation = $url;

		if($request->has('rfc')){
			$application->rfc = $request->input('rfc');
		}

		$application->save();

		return $application;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$application = Application::findOrfail($id);
		return response()->json($application->delete());
	}

	/**
	 * Display the status of the application.
	 *
	 *
	 * @OA\Get(
	 *      path="/application/status",
	 *      tags={"Solicitudes Estatus"},
	 *      @OA\Parameter(
	 *         name="id",
	 *         in="path",
	 *         description="Id de la solicitud",
	 *         required=true,
	 *      ),
	 *      summary="Obtener  el estatus de la solicitud",
	 *      description="Obtener  el estatus de la solicitud",
	 *      @OA\Response(
	 *           response="200", 
	 *           description="Exito",
	 *           @OA\JsonContent(
	 *              type="array",
	 *              @OA\Items(ref="#/components/schemas/Application")
	 *          )
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function getStatus(Request $request)
	{	
		$applications = Application::where('user_id',$request->user()->id)
			->orderBy('created_at')
			->get();
		return $applications;
	}
}
