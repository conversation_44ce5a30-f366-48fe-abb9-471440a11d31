<?php

namespace App\Http\Controllers;

use App\Model\Raffle;
use App\Model\RaffleDigitalPrize;
use App\Http\Requests\RaffleDigitalPrizeAdd as RaffleDigitalPrizeAddRequest;
use Carbon\Carbon;

class RaffleDigitalPrizeController extends Controller
{
	/**
	 * Update the specified resource in storage.
	 *
	 * @OA\Post(
	 *      path="/raffle/{raffle}/digital-prize",
	 *      tags={"Rifa","Digital","Premio","Instrucciones"},
	 *      @OA\Parameter(
	 *         name="raffle",
	 *         in="path",
	 *         description="Id de la tabla raffles",
	 *         required=true,
	 *      ),
	 *      summary="Actualiza las instrucciones parra canjear el premio",
	 *      description="Actualiza las instrucciones parra canjear el premio",
	 *      @OA\RequestBody(
	 *          required=true,
	 *           @OA\MediaType(
	 *               mediaType="application/json",
	 *               @OA\Schema(
	 *                   ref="#/components/schemas/RaffleDigitalPrizeUpdateInstructions"
	 *               ),
	 *           ),
	 *      ),
	 *      @OA\Response(
	 *           response="200", description="Exito",
	 *           @OA\JsonContent(ref="#/components/schemas/RaffleDigitalPrize")
	 *      ),
	 *      security={{"bearerAuth":{}}}
	 * )
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function addDigitalPrize(RaffleDigitalPrizeAddRequest $request, Raffle $raffle)
	{
		$raffleDigitalPrize = RaffleDigitalPrize::where('raffle_id', $raffle->id)->first();

		if ($raffleDigitalPrize) {
			$raffleDigitalPrize->instructions = $request->instructions;
			$raffleDigitalPrize->redeem_code  = $request->redeem_code;
			$raffleDigitalPrize->external_link = $request->external_link;
		} else {
			$raffleDigitalPrize = RaffleDigitalPrize::create([
				'raffle_id'     => $raffle->id,
				'instructions'  => $request->instructions,
				'redeem_code'   => $request->redeem_code,
				'external_link' => $request->external_link,
			]);
		}

		$url = '';
		if (request()->hasFile('instructions_file')) {
			$url = $this->uploadFile(
				$request->file('instructions_file'),
				Carbon::now()->timestamp . request()->file('instructions_file')->getClientOriginalName(),
				'rafiki/raffle_prize'
			);

			$raffleDigitalPrize->url_instructions = $url;
		}

		$raffleDigitalPrize->save();

		return $raffleDigitalPrize;
	}
}
