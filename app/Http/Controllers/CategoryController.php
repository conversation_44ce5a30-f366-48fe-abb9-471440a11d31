<?php

namespace App\Http\Controllers;

use App\Model\Category;
use App\Model\Subcategory;

use Illuminate\Http\Request;

class CategoryController extends Controller
{
	/**
     * @OA\Get(
     *      path="/category",
     *      tags={"Catalogos"},
     *      summary="Listado de categorias",
     *      description="Listado de categorias",
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/CatalogDescription")
     *          )
     *      )
     * )
     */
	public function index()
	{
		return Category::all();
	}

	/**
	 * Store a newly created resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @return \Illuminate\Http\Response
	 */
	public function store(Request $request)
	{
		$category = new Category();
		$category->fill($request->all());
		$category->save();

		return $category;
	}

	/**
	 * Display the specified resource.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function show($id)
	{
		return Category::findOrfail($id);
	}

	/**
	 * Update the specified resource in storage.
	 *
	 * @param  \Illuminate\Http\Request  $request
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function update(Request $request, $id)
	{
		$category = Category::findOrfail($id);
		$category->fill($request->all());
		$category->save();

		return $category;
	}

	/**
	 * Remove the specified resource from storage.
	 *
	 * @param  int  $id
	 * @return \Illuminate\Http\Response
	 */
	public function destroy($id)
	{
		$category = Category::findOrfail($id);
		return response()->json($category->delete());
	}

	/**
	 * Display a listing of the resource.
	 *
     * @OA\Get(
     *      path="/category/{id}/subcategories",
     *      tags={"Catalogos"},
     *      @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Id de la categoría",
     *         required=true,
     *      ),
     *      summary="Listado de subcategorias de una categoría",
     *      description="Listado de subcategorias de una categoría",
     *      @OA\Response(
     *           response="200", 
     *           description="Exito",
     *           @OA\JsonContent(
     *              type="array",
     *              @OA\Items(ref="#/components/schemas/Subcategory")
     *          )
     *      )
     * )
	 *
	 * @return \Illuminate\Http\Response
	 */
	public function subcategories($category)
	{
		return Subcategory::where('category_id', $category)->get();
	}
}
