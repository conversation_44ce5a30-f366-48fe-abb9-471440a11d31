<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Model\Test;
use App\Model\Player;
use App\Model\PlayerEvent;

class ImportPlayerResultTemplate implements FromView
{
    protected $event;
    protected $min;
    protected $max;

	public function __construct($event,$min,$max)
    {
        $this->event = $event;
        $this->min = $min;
        $this->max = $max;
    }

    public function view(): View
    {
    	$players = [];
    	if($this->min == 0 && $this->max == 0){
    		$playerIds = PlayerEvent::where('event_id',$this->event)
    				->get()
                    ->pluck('player_id');

    	}

    	if($this->min == 0 && $this->max != 0){
    		$playerIds = PlayerEvent::where('event_id',$this->event)
    				->where('number','<=',$this->max)
    				->get()
                    ->pluck('player_id');
    	}

    	if($this->min != 0 && $this->max == 0){
    		$playerIds = PlayerEvent::where('event_id',$this->event)
    				->where('number','>=',$this->min)
    				->get()
                    ->pluck('player_id');
    	}

    	if($this->min != 0 && $this->max != 0){
    		$playerIds = PlayerEvent::where('event_id',$this->event)
    				->where('number','>=',$this->min)
    				->where('number','<=',$this->max)
    				->get()
                    ->pluck('player_id');
    	}

        $players = Player::whereIn('id',$playerIds)->get();

    	$tests = Test::where('active',1)->get();
    	

        return view('exports.importPlayerResultTemplate', [
            'tests' => $tests,
            'players' => $players
        ]);
    }
}
