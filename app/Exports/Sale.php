<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Http\Controllers\ReportController;
use Carbon\Carbon;


class Sale implements FromView
{
    public function view(): View
    {
        $controller = new ReportController();
        $report = $controller->sales();


        return view('exports.sale', [
            'data' => $report,
            'date' => Carbon::now() 
        ]);
    }
}
