<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Http\Controllers\ReportController;
use Carbon\Carbon;

class Raffle implements FromView
{
    public function view(): View
    {
        $controller = new ReportController();
        $report = $controller->raffles();


        return view('exports.raffle', [
            'data' => $report,
            'date' => Carbon::now() 
        ]);
    }
}
