<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Model\MassificationPlayer;
use App\Model\MassificationCampus;

use Carbon\Carbon;

class MassificationCampusPlayerExport implements FromView
{
    protected $id;

	public function __construct($id)
    {
        $this->id = $id;
    }

    public function view(): View
    {
    	$players = MassificationPlayer::where('massification_campus_id',$this->id)->get();

    	$campus = MassificationCampus::findOrFail($this->id);

        return view('exports.massificationCampusPlayer', [
            'players' => $players,
            'campus' => $campus,
            'date' => Carbon::now() 
        ]);
    }
}
