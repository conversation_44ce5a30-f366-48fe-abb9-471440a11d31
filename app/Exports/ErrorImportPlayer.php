<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

use App\Model\CatGender;
use App\Model\CatState;
use App\Model\Campus;
use App\Model\CatDominantHand;
use App\Model\CatPosition;

use Carbon\Carbon;

class ErrorImportPlayer implements WithMultipleSheets
{
    use Exportable;


	protected $errors;
    
    public function __construct($errors)
    {
        $this->errors = $errors;
    }
    
    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
   
        $sheets[] = new PlayerImportErrorsSheetTemplate($this->errors);

        $sheets[] = new CatalogSheet(CatGender::all(),'Catálogo Genero');
        $sheets[] = new CatalogSheet(CatState::all(),'Catálogo Estados');
        $sheets[] = new CatalogSheet(Campus::all(),'Catálogo Sedes');
        $sheets[] = new CatalogSheet(CatDominantHand::all(),'Catálogo Mano dominante');
        $sheets[] = new CatalogSheet(CatPosition::all(),'Catálogo Posición');

        return $sheets;
    }
}
