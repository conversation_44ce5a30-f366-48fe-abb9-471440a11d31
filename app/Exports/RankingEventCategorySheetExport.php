<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

use Carbon\Carbon;

class RankingEventCategorySheetExport implements FromView, WithTitle
{
	protected $category;

	public function __construct($category)
    {
        $this->category = $category;
    }

    public function view(): View
    {
        return view('exports.rankingEventCategory', [
            'category' => $this->category,
            'date' => Carbon::now(),
        ]);
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->category->name;
    }
}