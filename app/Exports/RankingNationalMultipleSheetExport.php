<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

use App\Http\Controllers\ResultController;

class RankingNationalMultipleSheetExport implements WithMultipleSheets
{
	use Exportable;
    protected $type;
    protected $cycle;

    public function __construct($type,$cycle)
    {
        $this->type = $type;
        $this->cycle = $cycle;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $controller = new ResultController();
    	$categories = $controller->getRankingNational($this->type,$this->cycle);

    	$sheets = [];

        foreach ($categories as $category) {
        	$sheets[] = new RankingNationalCategorySheetExport($category);	
        }

        if(count($sheets) == 0){
            $sheets[] = new NoResultSheet();  
        }

        return $sheets;
    }
}
