<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Http\Controllers\ReportController;
use Carbon\Carbon;

class Winner implements FromView
{
    public function view(): View
    {
        $controller = new ReportController();
        $report = $controller->winners();


        return view('exports.winner', [
            'data' => $report,
            'date' => Carbon::now() 
        ]);
    }
}
