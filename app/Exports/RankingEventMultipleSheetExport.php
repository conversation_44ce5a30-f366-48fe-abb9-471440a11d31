<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

use App\Http\Controllers\ResultController;

class RankingEventMultipleSheetExport implements WithMultipleSheets
{
	use Exportable;


	protected $id;
    protected $type;
    protected $phase;
    
    public function __construct(int $id,  $phase = 0)
    {
        $this->id = $id;
        $this->phase = $phase;
    }
    
    /**
     * @return array
     */
    public function sheets(): array
    {
        $controller = new ResultController();
    	$categories = $controller->getRankingEvent($this->id, $this->phase);

    	$sheets = [];

        foreach ($categories as $category) {
        	$sheets[] = new RankingEventCategorySheetExport($category);	
        }

        if(count($sheets) == 0){
            $sheets[] = new NoResultSheet();  
        }

        return $sheets;
    }
}
