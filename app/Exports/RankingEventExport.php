<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Http\Controllers\EventController;
use Carbon\Carbon;

class RankingEventExport implements FromView
{
	protected $id;

	public function __construct($id)
    {
        $this->id = $id;
    }

    public function view(): View
    {
    	$controller = new ResultController();
    	$ranking = $controller->ResultController($this->id);

    	$event = $controller->show($this->id);

        return view('exports.rankingEvent', [
            'data' => $ranking,
            'event' => $event,
            'date' => Carbon::now() 
        ]);
    }

}
