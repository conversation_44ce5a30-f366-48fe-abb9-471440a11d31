<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class PlayerImportErrorsSheetTemplate implements FromView, WithTitle
{

	protected $errors;
    
    public function __construct($errors)
    {
        $this->errors = $errors;
    }

    public function view(): View
    {
        return view('exports.importPlayerErrorEvent',[
        	'data' => $this->errors
        ]);
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Jugadores';
    }
}
