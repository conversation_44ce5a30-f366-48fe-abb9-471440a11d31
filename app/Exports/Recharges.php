<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Http\Controllers\ReportController;
use Carbon\Carbon;

class Recharges implements FromView
{
    public function view(): View
    {
        $controller = new ReportController();
        $report = $controller->recharges();


        return view('exports.recharge', [
            'data' => $report,
            'date' => Carbon::now() 
        ]);
    }
}
