<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

use App\Http\Controllers\ResultController;
use App\Http\Controllers\EventController;

class EventResultMultipleSheetExport implements WithMultipleSheets
{
    use Exportable;


	protected $id;
    
    public function __construct(int $id)
    {
        $this->id = $id;
    }
    
    /**
     * @return array
     */
    public function sheets(): array
    {
        //$event = new EventController();
        //$eventRanks = $event->processRanks($this->id);
        $controller = new ResultController();
    	$categories = $controller->eventResult($this->id);

    	$sheets = [];

        foreach ($categories as $category) {
        	$sheets[] = new EventResultCategorySheetExport($category);	
        }

        if(count($sheets) == 0){
            $sheets[] = new NoResultSheet();  
        }

        return $sheets;
    }
}
