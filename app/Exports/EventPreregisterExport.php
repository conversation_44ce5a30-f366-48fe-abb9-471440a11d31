<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Model\Preregister;
use App\Model\Event;

use Carbon\Carbon;

class EventPreregisterExport implements FromView
{
    protected $id;

	public function __construct($id)
    {
        $this->id = $id;
    }

    public function view(): View
    {
    	$event = Event::findOrFail($this->id);

    	$players = Preregister::where('event_id',$this->id)->get();

        return view('exports.exportEventPreregister', [
            'players' => $players,
            'event' => $event,
            'date' => Carbon::now() 
        ]);
    }
}
