<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

use App\Model\Player;
use App\Model\Category;
use App\Model\Campus;

use Carbon\Carbon;

class CampusPlayerExport implements FromView
{
    protected $id;
    protected $start;
    protected $end;

	public function __construct($id,$start,$end)
    {
        $this->id = $id;
        $this->start = $start;
        $this->end = $end;
    }

    public function view(): View
    {
    	$categoryIds = Player::select('category_id')
    				->distinct()
    				->where('campus_id',$this->id)
    				->where('number','>=',$this->start)
    				->where('number','<=',$this->end)
    				->get()
    				->pluck('category_id');

    	$categories = Category::whereIn('id',$categoryIds)->get();

    	foreach ($categories as $category) {
    		$category->players = Player::where('campus_id',$this->id)
    				->where('number','>=',$this->start)
    				->where('number','<=',$this->end)
    				->where('category_id',$category->id)
    				->orderBy('number')
    				->get();
    	}

    	$campus = Campus::findOrFail($this->id);

        return view('exports.campusPlayer', [
            'data' => $categories,
            'campus' => $campus,
            'date' => Carbon::now() 
        ]);
    }
}
