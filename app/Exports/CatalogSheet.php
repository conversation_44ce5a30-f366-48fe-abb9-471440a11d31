<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithTitle;

class CatalogSheet implements FromView, WithTitle
{

	protected $name;
	protected $data;

	public function __construct($data,$name)
    {
        $this->name = $name;
        $this->data = $data;
    }

    public function view(): View
    {
        return view('exports.catalogSheet',[
        	'data' => $this->data
        ]);
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->name;
    }
}
