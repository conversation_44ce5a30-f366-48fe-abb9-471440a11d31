<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Subcategory extends Model
{
	public $with = ['category'];

	protected $fillable = [
		'category_id',
		'name',
		'description',
		'active',
	];

	/**
	 * Get the category that owns the subcategory.
	 */
	public function category()
	{
		return $this->belongsTo(Category::class);
	}

	public function scopeActive($query)
    {
        return $query->where('active',true);
    }
}
