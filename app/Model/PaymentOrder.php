<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class PaymentOrder extends Model
{
    protected $table = 'payment_orders';  

    public $appends = ['tickets_formatted'];       

    protected $fillable = [
        'num_tickets',
        'payment_total',
        'commission',
        'payment_type',
        'status',
        'raffle_id',
        'card_id',
        'buyer_id',
    ];

    public function tickets(){
        return $this->belongsToMany('App\Model\Ticket', 'ticket_payment_order', 'payment_order_id','ticket_id');
    }

    public function getTicketsFormattedAttribute(){
        $tickets = [];

        foreach ($this->tickets as $ticket ) {
            $tickets[] = $ticket->number;
        }

        return implode(',',$tickets);

    }
}
