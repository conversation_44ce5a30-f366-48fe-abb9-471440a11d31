<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use DB;
use App\User;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

use App\Model\RafflerPayment;
use App\Model\PaymentOrder;
use App\Model\CancelReport;
use App\Model\CancelReportPaymentOrder;
use Illuminate\Support\Facades\Log;

use App\Model\Application;
use App\Model\Notification;
use App\Helpers\AppHelper;

use Illuminate\Support\Facades\Mail;
use App\Mail\Winner;
use App\Mail\RaffleDraw as DrawMail;
use App\Mail\CancelRaffle;

class Raffle extends Model
{
	protected $table = 'raffles';
	protected $with = ['raffler','state','report','digitalPrize'];

	protected $appends = ['sold_tickets', 'status', 'tops', 'min_percentage','image','slug'];

	protected $fillable = [
		'name',
		'brand',
		'model',
		'description',
		'cat_state_id',
		'city',
		'start',
		'finish',
		'prize_value',
		'num_tickets',
		'ticket_price',
		'suggested_value',
		'active',
		'canceled',
		'cancel_comments',
		'product_type',
		'prize_type',
		'subcategory_id',
		'user_id',
	];

	public static function boot()
	{
		parent::boot();

		self::updated(function ($raffle) {
			if ($raffle->canceled != $raffle->getOriginal('canceled') && $raffle->canceled) {
				$raffle->generateRefundTransactions();
				//$raffle->chargedRaffler();
				$raffle->cancelNotifications();
			}
		});
	}

	public function chargedRaffler(){

		$cancelReport = CancelReport::where('raffle_id',$this->id)
					->where('status','success')
					->first();

		if($cancelReport != null){
			return response()->json([
						'error' => 'Charge fee previously success',
						'message' => 'Charge fee previously success',
					]);
		}

		$orders = PaymentOrder::where('raffle_id',$this->id)
					->where('status','success')
					->get();

		$charge = 0;
		foreach ($orders as $order) {
			if($order->payment_type == 'Conekta'){
				$charge += $order->commission;
			}
		}

		if($charge > 0){
			//charge with conekta

			$card = Card::where('user_id',$this->user_id)
				->active()
				->first();

			if($card == null){
				//Card not available
				$this->createCancelReport(
						$charge,
						'error',
						$orders,
						[],
						null,
						[
							'error' => 'Card not available',
							'message' => 'Card not available',
						]
				);

				return response()->json([
						'error' => 'Card not available',
						'message' => 'Card not available',
					]);
			}

			$user = User::findOrFail($this->user_id);


			$card->makeVisible(['token', 'source_id']);

			$data = (object) [
				"user" => $user->makeVisible(['conekta_source_id']),
				"card" => $card,
				"total" => number_format($charge,2),
				"id" => $user->id,
			];

			//crear informacion necesaria para conekta antes de generar el pago
			try {
				$response = _Conekta::_createCustomer($data);

				Log::debug(json_encode($response));
			} catch (\Exception $ex) {


				$this->createCancelReport(
						$charge,
						'error',
						$orders,
						$data,
						null,
						[
							'error' => 'Conekta error - Create User',
							'message' => $ex->getMessage(),
						]
				);

				return response()->json($ex->getMessage(), 500);
			}

			try {
				DB::beginTransaction();
				//realizar el cargo correspondiente mediante conekta
				$responseOrder = _Conekta::_createOrder($response);

				DB::commit();
			} catch (\Exception $ex) {
				$this->createCancelReport(
						$charge,
						'error',
						$orders,
						$response,
						null,
						[
							'error' => 'Conekta error - Create Order',
							'message' => $ex->getMessage(),
						]
				);

				return response()->json($ex->getMessage(), 500);
			}

			$this->createCancelReport(
						$charge,
						'success',
						$orders,
						$responseOrder,
						$card->id
				);
		}else{
			$this->createCancelReport($charge,'success',$orders,[]);
		}

		return $charge;
	}

	public function cancelNotifications(){
		$raffle = $this;
		$this->cancelNotificationAdmin($raffle);
		$this->cancelNotificationCustomer($raffle);
	}

	private function cancelNotificationAdmin($raffle){

		$data = [
			'click_action' => "FLUTTER_NOTIFICATION_CLICK",
			'raffle_id' => $raffle->id,
			'user_id' => $raffle->user_id,
		];

		Notification::create([
			'title'   => 'Rifa cancelada',
			'message' => 'Rifa '.$raffle->name .' cancelada',
			'data'    => $raffle->toJson(),
			'user_id' => $raffle->user_id,
			'cat_notification_type_id' => 10
		]);

		try{
			AppHelper::sendPushNotification(
				$raffle->user_id,
				'Rifa cancelada',
				'Rifa '.$raffle->name .' cancelada',
				$data
			);
		}catch(\Exception $e){
			Log::debug($e->getMessage());
		}


		$user = User::find($raffle->user_id);
		/*$cancelReport = CancelReport::where('raffle_id',$raffle->id)
			->where('status','success')
			->first();*/

		try{
			Mail::to($user->email)
			->send(
				//new CancelRaffle($raffle, $cancelReport)
				new CancelRaffle($raffle)
			);
		}catch(\Exception $e){
			Log::debug($e->getMessage());
		}
	}

	private function cancelNotificationCustomer($raffle){
		$users = Ticket::select('buyer_id')
			->distinct()
			->where('status','sold')
			->where('raffle_id',$raffle->id)
			->get()
			->pluck('buyer_id');

		foreach ($users as $user) {
			$data = [
				'click_action' => "FLUTTER_NOTIFICATION_CLICK",
				'raffle_id' => $raffle->id,
				'user_id' => $user,
			];

			Notification::create([
				'title'   => 'Rifa '.$raffle->name .' cancelada',
				'message' => 'La rifa fue cancelada, en breve se ejecutará tu reembolso.',
				'data'    => $raffle->toJson(),
				'user_id' => $user,
				'cat_notification_type_id' => 11
			]);

			try{
				AppHelper::sendPushNotification(
					$user,
					'Rifa '.$raffle->name .' cancelada',
					'La rifa fue cancelada, en breve se ejecutará tu reembolso.',
					$data
				);
			}catch(\Exception $e){
				Log::debug($e->getMessage());
			}
		}

	}

	private function createCancelReport($charge, $status, $orders, $data, $card = null, $error = null){
		$report = new CancelReport;
		$report->raffle_id = $this->id;
		$report->tickets_sold = $this->sold_tickets;
		$report->commission = $charge;
		$report->status = $status;
		$report->data = json_encode($data);
		$report->user_id = $this->user_id;

		if($card != null){
			$report->card_id = $card;
		}

		if($error != null){
			$report->error = json_encode($error);
		}

		$report->save();

		foreach ($orders as $order) {
			$reportOrder = new CancelReportPaymentOrder;
			$reportOrder->cancel_report_id = $report->id;
			$reportOrder->payment_order_id = $order->id;
			$reportOrder->save();
		}

		return true;
	}

	public function generateRefundTransactions()
	{
		$transactions = Ticket::SelectRaw('buyer_id as user_id, SUM(r.ticket_price) as total')
							   ->join('raffles as r', 'r.id', 'tickets.raffle_id')
							   ->where('tickets.raffle_id', $this->id)
							   ->where('tickets.status', 'sold')
							   ->groupBy('tickets.buyer_id')
			  				   ->get();

		foreach($transactions as $transaction) {
			Balance::create([
				'raffle_id'   => $this->id,
				'description' => 'Devolución por rifa cancelada',
				'amount'      => $transaction->total,
				'user_id'     => $transaction->user_id
			]);
		}
	}

	public function raffleWinner()
	{
		return $this->hasOne(RaffleWinner::class, 'raffle_id', 'id')
			->with('ticket');
	}

	public function digitalPrize()
	{
		return $this->hasOne(RaffleDigitalPrize::class, 'raffle_id', 'id');
	}

	public function draw()
	{
		return $this->hasOne(RaffleDraw::class);
	}

	public function report()
	{
		return $this->hasOne(RaffleReport::class)
			->setEagerLoads([])
			->select(['id','comments','admin_comments','status','raffle_id'])
			->where('status','accepted');
	}

	public function getStatusAttribute()
	{
		if ($this->canceled) {
			return 'canceled';
		}

		if(!$this->raffleWinner && ($this->finish < Carbon::now())){
			return 'not_raffled';
		}

		if ($this->raffleWinner) {
			return  'raffled';
		} else {
			return 'pending';
		}
	}

	/**
	 * Get the category that owns the subcategory.
	 */
	public function subcategory()
	{
		return $this->belongsTo(Subcategory::class);
	}

	/**
	 * Get the category that owns the subcategory.
	 */
	public function state()
	{
		return $this->belongsTo(CatState::class,'cat_state_id')->select([
			'id',
			'name'
		]);
	}

	/**
	 * Get the category that owns the raffler.
	 */
	public function raffler()
	{
		return $this->belongsTo(User::class,  'user_id', 'id');
	}

	/**
	 * Get the category that owns the subcategory.
	 */
	public function images()
	{
		return $this->hasMany(RafflePhoto::class);
	}

	public function getImageAttribute()
	{
		$photo = RafflePhoto::where(
				[
					"raffle_id" => $this->id,
					"default" => 1,
				]
			)
			->first();
		if($photo == null){
			return null;
		}
		return $photo->url_image;
	}

	public function scopeActive($query)
	{
		return $query->where('active', true)
			->where('start','<=',Carbon::now())
			->where('finish','>=',Carbon::now())
			->where('canceled',false);
	}

	public function tickets()
	{
		return $this->hasMany(Ticket::class)->select(
			[
				'id',
				'number',
				// 'locked',
                DB::raw("locked_finish > CONVERT_TZ(now(), '+00:00', '-06:00') as 'locked'"),
				'locked_finish',
				'status',
				'raffle_id',
				'buyer_id'
			]
		);
	}

	public function buyed_tickets()
	{
		$user = 0;

		if (Auth::user()) {
			$user = Auth::user()->id;
		}

		return $this->hasMany(Ticket::class)->select(
			[
				'number',
				'status',
				'raffle_id',
				'buyer_id',
				'fast_purchase',
				'payment_type',
				'buy_date',
				'winner'
			]
		)->where('buyer_id', $user);
	}

	public function getSoldTicketsAttribute()
	{
		$tickets = DB::table('tickets')
			->where(
				[
					"raffle_id" => $this->id,
					"status" => "Sold",
				]
			)
			->count();
		return $tickets;
	}

	public function getTopsAttribute()
	{
		$tickets = DB::table('tickets')
			->selectRaw('count(id) as total')
			->where(
				[
					"raffle_id" => $this->id,
					"status" => "Sold",
				]
			)
			->distinct()
			->groupBy('buyer_id')
			->limit(5)
			->get();


		return $tickets;
	}

	public function getMinPercentageAttribute()
	{
		//TODO: cambiar para la prueba
		$tickets = 0;
		$total = $this->prize_value * 2;
		if($this->prize_value == null){
			return 0;
		}
		$tickets = ceil($total / $this->ticket_price);
		$percentage = ceil(($tickets/$this->num_tickets * 100));
		return $percentage;
	}

	public function create_payment()
	{
		$payments = RafflerPayment::where('raffle_id', $this->id)
			->whereIn('status', ['pending', 'accepted'])
			->count();

		if ($payments > 0) {
			return false;
		}

		$total_amount = $this->ticket_price * $this->sold_tickets;
		$commission = $total_amount * .2;
        $commission_tax = $commission * 0.16;

        $application = Application::where('user_id',$this->user_id)
            ->where('approval',true)
            ->first();

        $isr_p = 20;
        if($application != null && ($application->rfc != null && $application->rfc != '')){
            $isr_p = 1;
        }

        $isr = $total_amount * ( $isr_p / 100);

        $deposit = $total_amount - $commission - $commission_tax - $isr;

		$payment = new RafflerPayment;
		$payment->ticket_amount = $total_amount;
		$payment->commission_amount = $commission;
		$payment->commission_iva = $commission_tax;
		$payment->isr_amount = $isr;
		$payment->isr_percentage = $isr_p;
		$payment->raffler_amount = $deposit;
		$payment->raffle_id =  $this->id;
		$payment->raffler_id =  $this->user_id;

		$payment->save();

		return $payment;
	}

	public function getSlugAttribute()
	{
		$removeSpecialCar = preg_replace('/[^A-Za-z0-9 ]/', '', $this->name);
		$removeSpace = str_replace(' ', '-', $removeSpecialCar);
		return strtolower($removeSpace);
	}
}
