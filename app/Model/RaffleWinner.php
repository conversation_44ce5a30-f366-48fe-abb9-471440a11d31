<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use App\User;

use App\Helpers\AppHelper;

class RaffleWinner extends Model
{
	protected $with = ['winner'];

	protected $fillable = [
		'winner_id',
		'raffle_id',
		'ticket_id',
		'address_id',
		'delivery_company',
		'track_id',
		'track_url',
		'estimated_delivery_date',
		'received_prize',
		'reception_date',
		'evidence_url'
	];

	public function winner()
	{
		return $this->belongsTo(User::class, 'winner_id', 'id')->select(['id','nickname']);
	}

	public function raffle()
	{
		return $this->belongsTo(Raffle::class, 'raffle_id', 'id');
	}

	public function ticket()
	{
		return $this->belongsTo(Ticket::class, 'ticket_id', 'id')->select(['id','number']);
	}

	public function address()
	{
		return $this->belongsTo(Address::class, 'address_id', 'id');
	}

	public static function boot()
	{
		parent::boot();

		self::updated(function ($raffleWinner) {
			if ($raffleWinner->address_id != $raffleWinner->getOriginal('address_id')) {
				if ($raffleWinner->address_id) {
					Notification::create([
						'title'   => 'Ganador de la Rifa - Actualización de dirección',
						'message' => 'Actualización de dirección',
						'data'    => $raffleWinner->toJson(),
						'user_id' => $raffleWinner->raffle->user_id,
						'cat_notification_type_id' => 5
					]);

					$data = [
						'click_action' => "FLUTTER_NOTIFICATION_CLICK",
						'raffle_id' => $raffleWinner->raffle_id,
					];

					try{
						AppHelper::sendPushNotification(
							$raffleWinner->raffle->user_id,
							'Ganador de la Rifa - Actualización de dirección',
							$raffleWinner->raffle->name.': Actualización de dirección',
							$data
						);
					}catch(\Exception $e){
						Log::debug($e->getMessage());
					}
				}
			}

			if ($raffleWinner->delivery_company != $raffleWinner->getOriginal('delivery_company')
				|| $raffleWinner->track_id != $raffleWinner->getOriginal('track_id')
				|| $raffleWinner->estimated_delivery_date != $raffleWinner->getOriginal('estimated_delivery_date')) {
				
				Notification::create([
					'title'   => 'Rifador - Actualización de información de entrega',
					'message' => 'Actualización de información de entrega',
					'data'    => $raffleWinner->toJson(),
					'user_id' => $raffleWinner->winner_id,
					'cat_notification_type_id' => 6
				]);

				$data = [
					'click_action' => "FLUTTER_NOTIFICATION_CLICK",
					'raffle_id' => $raffleWinner->raffle_id,
				];

				try{
					AppHelper::sendPushNotification(
						$raffleWinner->winner_id,
						'Rifador - Actualización de información de entrega',
						$raffleWinner->raffle->name.': Actualización de información de entrega',
						$data
					);
				}catch(\Exception $e){
					Log::debug($e->getMessage());
				}


			}

			if ($raffleWinner->received_prize != $raffleWinner->getOriginal('received_prize')) {
				if ($raffleWinner->received_prize) {
					Notification::create([
						'title'   => 'Ganador de la Rifa - Recibió el premio',
						'message' => 'Ganador recibió el premio',
						'data'    => $raffleWinner->toJson(),
						'user_id' => $raffleWinner->raffle->user_id,
						'cat_notification_type_id' => 7
					]);

					$raffleWinner->raffle->create_payment();

					$data = [
						'click_action' => "FLUTTER_NOTIFICATION_CLICK",
						'raffle_id' => $raffleWinner->raffle_id,
					];

					try{
						AppHelper::sendPushNotification(
							$raffleWinner->raffle->user_id,
							'Ganador de la Rifa - Recibió el premio',
							$raffleWinner->raffle->name.': Ganador recibió el premio',
							$data
						);
					}catch(\Exception $e){
						Log::debug($e->getMessage());
					}
				}
			}
		});
	}
}
