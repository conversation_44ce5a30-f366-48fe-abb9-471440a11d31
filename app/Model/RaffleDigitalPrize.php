<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

use App\Helpers\AppHelper;

class RaffleDigitalPrize extends Model
{
	protected $fillable = [
		'raffle_id',
		'instructions',
		'redeem_code',
		'external_link',
		'url_instructions',
	];

	public function raffle()
	{
		return $this->belongsTo(Raffle::class, 'raffle_id', 'id');
	}

	public static function boot()
	{
		parent::boot();

		self::updated(function ($raffleDigitalPrize) {
			if ($raffleDigitalPrize->instructions != $raffleDigitalPrize->getOriginal('instructions')
				|| $raffleDigitalPrize->redeem_code != $raffleDigitalPrize->getOriginal('redeem_code')
				|| $raffleDigitalPrize->external_link != $raffleDigitalPrize->getOriginal('external_link')
				|| $raffleDigitalPrize->url_instructions != $raffleDigitalPrize->getOriginal('url_instructions')
			) {
				
				Notification::create([
					'title'   => 'Actualización de instrucciones del premio digital',
					'message' => 'Actualización de instrucciones del premio digital',
					'data'    => $raffleDigitalPrize->toJson(),
					'user_id' => $raffleDigitalPrize->raffle->raffleWinner->winner_id,
					'cat_notification_type_id' => 8
				]);

				$data = [
					'click_action' => "FLUTTER_NOTIFICATION_CLICK",
					'raffle_id' => $raffleDigitalPrize->raffle_id,
				];

				try{
					AppHelper::sendPushNotification(
						$raffleDigitalPrize->raffle->raffleWinner->winner_id,
						$raffleDigitalPrize->raffle.' - Actualización de instrucciones del premio digital',
						'Actualización de instrucciones del premio digital',
						$data
					);
				}catch(\Exception $e){
					Log::debug($e->getMessage());
				}
				
			}
		});
	}
}
