<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

use App\User;
use App\Model\Raffle;

class CancelReport extends Model
{
    protected $table = 'cancel_reports';   

    protected $with = ['user', 'raffle'];

    protected $appends = ['cancel_comment','total_formatted'];

    protected $fillable = [
        'tickets_sold',
        'commission',
        'status',
        'data',
        'error',
        'raffle_id',
        'user_id',
        'card_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function raffle()
    {
        return $this->belongsTo(Raffle::class, 'raffle_id', 'id');
    }

    public function getCancelCommentAttribute()
    {
        return $this->raffle->cancel_comments ?: $this->raffle->report->admin_comments ;
    }

    public function getTotalFormattedAttribute()
    {
        return "$ " . number_format($this->commission, 2);
    }

    public function getRafikiOrdersAttribute(){
        $orders = [];

        foreach ($this->orders as $order) {
            if($order->payment_type =='Rafiki'){
                $orders[] = $order;
            }
        }
        return $orders;
    }

    public function getConektaOrdersAttribute(){
        $orders = [];

        foreach ($this->orders as $order) {
            if($order->payment_type =='Conekta'){
                $orders[] = $order;
            }
        }

        return $orders;
    }

    public function orders(){
        return $this->belongsToMany('App\Model\PaymentOrder', 'cancel_report_payment_order', 'cancel_report_id', 'payment_order_id')->with('tickets');
    }
}
