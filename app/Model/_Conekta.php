<?php

namespace App\Model;

use Conekta\Conekta;
use Exception;

use Illuminate\Support\Facades\Log;

class _Conekta {

    /**
     * Create customer to Conekta
     *
     * @param $data
     * @param $items
     * @return mixed
     * @throws Exception
     */
    public static function _createCustomer($data) {

        Conekta::setApiKey(config('app.conekta_private_key'));
        Conekta::setApiVersion("2.0.0");

        $card = $data->card;
        $user = $data->user;

        Log::debug('-- Create customer - Conekta Payment --');
        Log::debug('$user');
        Log::debug($user);
        Log::debug('$card');
        Log::debug($card);

        $customer = self::getCustomer($user->conekta_source_id);

        if ($customer) {
            self::updateCustomer($customer, $card);
        } else {
            self::createConektaCustomer($user, $card);
        }

        return $data;
    }

    public static function getCustomer($customerId) {
        try {
            return \Conekta\Customer::find($customerId);
        } catch (Exception $ex) {
            Log::debug('--get customer--');
            Log::debug($ex->getMessage());
            Log::debug($ex->getTraceAsString());
            return false;
        }
    }

    public static function createConektaCustomer($user, $card) {
        Log::debug('--createConektaCustomer--');
        Log::debug('--user--');
        Log::debug($user);

        try {
            $customer = \Conekta\Customer::create([
                "name" => $user->name,
                "email" => $user->email,
                "payment_sources" => [
                    [
                        "token_id" => $card->token,
                        "type" => "card"
                    ]
                ],
            ]);

            Log::debug('-- $customer --');
            Log::debug($customer);

            $user->update([
                'conekta_source_id' => $customer->id
            ]);

            Log::debug('-- $user --');
            Log::debug($user);

            $card->update([
                'source_id' => $customer->default_payment_source_id
            ]);

            Log::debug($card);
            return $customer;
        } catch (\Conekta\ProcessingError $error) {
            Log::debug('--Create customer ProcessingError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\ParameterValidationError $error) {
            Log::debug('--Create customer ParameterValidationError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\Handler $error) {
            Log::debug('--Create customer Handler--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        }
    }

    public static function updateCustomer($customer, $card) {
        Log::debug('--updateCustomer--');
        try {
            if (!isset($card->source_id)) {
                Log::debug('-- createPaymentSource --');
                $source = $customer->createPaymentSource([
                    "token_id" => $card->token,
                    "type" => "card"
                ]);

                Log::debug('-- $card->update --');
                $card->update([
                    'source_id' => $source->id
                ]);
            }

            if ($customer->default_payment_source_id !== $card->source_id) {
                 Log::debug('-- $card->default_payment_source_id --');
                $customer->update([
                    'default_payment_source_id' => $card->source_id
                ]);
            }

        } catch (\Conekta\ProcessingError $error) {
            Log::debug('--Create customer ProcessingError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\ParameterValidationError $error) {
            Log::debug('--Create customer ParameterValidationError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\Handler $error) {
            Log::debug('--Create customer Handler--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        }
    }

    public static function _createOrder($data) {
        Conekta::setApiKey(config('app.conekta_private_key'));
        Conekta::setApiVersion("2.0.0");

        Log::debug('--_createOrder--');
        Log::debug(json_encode($data));
        
        $address = array(
            "address" => array(
                "street1" => "NA",
                "postal_code" => "00000",
                "country" => "MX"
            )
        );

        Log::debug('-- Total --');
        Log::debug($data->total);
        $total = $data->total;

        Log::debug('-- Total --');
        Log::debug($total * 100);

        $total = $total * 100;
        
        try {
            $order = \Conekta\Order::create(
                [
                    "line_items" => [ 
                        [
                            "name" => "Rafiki compra general",
                            "unit_price" => $total,
                            "quantity" => 1
                        ]
                    ],
                    "shipping_lines" => [
                        [
                            "amount" => 00,
                            "carrier" => "N/A"
                        ]
                    ], //shipping_lines - physical goods only
                    "currency" => "MXN",
                    "customer_info" => [
                        "customer_id" => $data->user->conekta_source_id
                    ], //customer_info
                    "shipping_contact" => $address,
                    "metadata" => [
                        "reference" => $data->id
                    ],
                    "charges" => [
                        [
                            "payment_method" => [
                                "type" => "default"
                            ] //payment_method - use customer's <code>default</code> - a card
                        ] //first charge
                    ] //charges
                ] //order
            );

            return $order;
        } catch (\Conekta\ProcessingError $error) {
            Log::debug('--Create customer ProcessingError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\ParameterValidationError $error) {
            Log::debug('--Create customer ParameterValidationError--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        } catch (\Conekta\Handler $error) {
            Log::debug('--Create customer Handler--');
            Log::debug($error->getMessage());
            Log::debug($error->getTraceAsString());
            throw new Exception($error->getMessage());
        }
    }

    public static function _updateOrder($conektaOrderId, $localOrderId) {
        Conekta::setApiKey(config('app.conekta_private_key'));
        Conekta::setApiVersion("2.0.0");

        try {
            $order = \Conekta\Order::find($conektaOrderId);
            $order->update(array(
                "metadata" => array(
                    "reference" => (string) $localOrderId,
                    "more_info" => "N/A"
                ),
            ));
        } catch (\Exception $error) {
            throw new Exception($error->getMessage());
        }
    }

    public static function _createAddress($data) {
        switch ($data['address_type']) {
            case 'address':
                $address = Address::find($data['address_id']);
                $fullAddress = $address->street . " " . $address->outdoor_number;

                Log::debug('$fullAddress');
                Log::debug($fullAddress);

                $currentAddress = array(
                    "address" => array(
                        "street1" => $fullAddress,
                        "postal_code" => (string)$address->postcode,
                        "country" => "MX"
                    )
                );
                
                break;
            default:
                $address = Shop::find($data['address_id']);

                Log::debug('$address->address');
                Log::debug($address->address);
                
                $currentAddress = array(
                    "address" => array(
                        "street1" => "NA",
                        "postal_code" => "00000",
                        "country" => "MX"
                    )
                );
                break;
        }

        return $currentAddress;
    }

}

