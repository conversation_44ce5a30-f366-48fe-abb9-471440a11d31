<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use App\User;

use Illuminate\Support\Facades\Mail;
use App\Mail\CancelApplication;
use App\Mail\AcceptApplication;

use App\Model\Notification;
use App\Helpers\AppHelper;

use Illuminate\Support\Facades\Log;

class Application extends Model
{
	protected $with = ['user', 'admin'];

	protected $fillable = [
		'url_id_front',
		'url_id_back',
		'url_bank_information',
		'rfc',
		'url_rfc_documentation',
		'approval',
		'approval_date',
		'rejection_reason',
		'user_id',
		'admin_id'
	];

	public function user()
	{
		return $this->belongsTo(User::class, 'user_id', 'id');
	}

	public function admin()
	{
		return $this->belongsTo(User::class, 'admin_id', 'id');
	}

	public static function boot()
	{
		parent::boot();

		self::updated(function ($application)  {
			if ($application->approval != $application->getOriginal('approval')) {

				$user = User::findOrFail($application->user_id);
				
				if ($application->approval) {

					try{
						$data = [
							'click_action' => "FLUTTER_NOTIFICATION_CLICK",
							'user_id' => $application->user_id,
							'application_id' => $application->id,
						];

						Notification::create([
							'title'   => 'Solicitud aprobada',
							'message' => 'Tu solicitud fue aprobada',
							'data'    => $application->toJson(),
							'user_id' => $application->user_id,
							'cat_notification_type_id' => 1
						]);

						AppHelper::sendPushNotification(
							$application->user_id,
							'Solicitud aprobada',
							'Tu solicitud fue aprobada',
							$data
						);
						
						Mail::to($user->email)
						->send(
							new AcceptApplication()
						);
					}catch(\Exception $e){
						Log::debug($e->getMessage());
						Log::debug($e->getTraceAsString());
					}
					
				} else {

					try{
						$data = [
							'click_action' => "FLUTTER_NOTIFICATION_CLICK",
							'user_id' => $application->user_id,
							'application_id' => $application->id,
						];

						Notification::create([
							'title'   => 'Solicitud rechazada',
							'message' => 'Tu solicitud fue rechazada',
							'data'    => $application->toJson(),
							'user_id' => $application->user_id,
							'cat_notification_type_id' => 2
						]);

						AppHelper::sendPushNotification(
							$application->user_id,
							'Se rechazó su solicitud',
							'Verifica los detalles en tu perfil',
							$data
						);
						
						Mail::to($user->email)
						->send(
							new CancelApplication($application)
						);
					
					}catch(\Exception $e){
						Log::debug($e->getMessage());
						Log::debug($e->getTraceAsString());
					}
				}
			}
		});
	}
}
