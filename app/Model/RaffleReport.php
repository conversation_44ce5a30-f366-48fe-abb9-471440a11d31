<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use App\User;

class RaffleReport extends Model
{
	protected $with = ['raffle', 'user', 'admin'];

	protected $fillable = [
		'raffle_id',
		'user_id',
		'admin_id',
		'status',
		'comments',
		'admin_comments',
		'reviewed_at',
	];

	public static function boot()
	{
		parent::boot();

		self::updating(function ($raffleReport) {
			if ($raffleReport->status !== $raffleReport->getOriginal('status')) {
				$raffleReport->reviewed_at = date('Y-m-d H:i:s');
			}
		});

		self::updated(function ($raffleReport) {
			if ($raffleReport->status === 'accepted' && $raffleReport->status !== $raffleReport->getOriginal('status')) {
				$raffle = $raffleReport->raffle;
				$raffle->canceled = 1;
				$raffle->save();
			}
		});
	}

	public function user()
	{
		return $this->belongsTo(User::class, 'user_id', 'id');
	}

	public function admin()
	{
		return $this->belongsTo(User::class, 'admin_id', 'id');
	}

	public function raffle()
	{
		return $this->belongsTo(Raffle::class, 'raffle_id', 'id');
	}
}
