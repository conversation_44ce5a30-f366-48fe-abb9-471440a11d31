<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
	protected $table = 'tickets';

	protected $fillable = [
		'number',
        'locked',
        'locked_finish',
        'buy_date',
        'fast_purchase',
        'payment_type',
        'status',
        'raffle_id',
        'buyer_id',
        'winner',
        'selection_time',
	];

	public function scopeFree($query)
    {
        return $query->where('status','Free');
    }


    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'locked_finish' => 'datetime',
        'buy_date' => 'datetime',
    ];
}
