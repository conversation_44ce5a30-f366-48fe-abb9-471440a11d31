<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
	protected $table = 'notifications';
	protected $with = ['type'];

	protected $fillable = [
		'title',
		'message',
		'data',
		'user_id',
		'readed',
		'created_at',
		'cat_notification_type_id',
	];

	public function type()
	{
		return $this->hasOne('App\Model\CatNotificationType', 'id', 'cat_notification_type_id');
	}
}
