<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use App\User;
use App\Model\Application;

class RafflerPayment extends Model
{
	protected $table = 'raffler_payments';

	protected $with = ['raffle'];

	protected $fillable = [
		'ticket_amount',
		'commission_amount',
		'commission_iva',
        'isr_amount',
        'isr_percentage',
		'raffler_amount',
		'evidence_url',
		'status',
		'cancelation_date',
		'accept_date',
		'comments',
		'raffle_id',
		'admin_id',
		'raffler_id',
	];

	protected $appends = [
		'can_generate_payment',
	];

	public function raffler()
	{
		return $this->belongsTo(User::class,  'raffler_id', 'id');
	}

	public function application()
    {
        return $this->belongsTo(Application::class,  'raffler_id', 'id')->where('approval',1);
    }

	public function raffle()
	{
		return $this->belongsTo(Raffle::class)->setEagerLoads([])
			->select(['id', 'name','num_tickets','ticket_price']);
	}

	public function getCanGeneratePaymentAttribute()
	{
		$existsPayment = RafflerPayment::whereIn('status', ['pending', 'accepted'])
			->where('raffle_id', $this->raffle_id)
			->exists();

		return $this->status === 'rejected' && !$existsPayment;
	}

	public static function boot()
	{
		parent::boot();

		self::updated(function ($rafflerPayment) {
			if ($rafflerPayment->evidence_url != $rafflerPayment->getOriginal('evidence_url')) {
				Notification::create([
					'title'   => 'Administrador - Subió comprobante de pago',
					'message' => 'Comprobante de pago',
					'data'    => $rafflerPayment->toJson(),
					'user_id' => $rafflerPayment->raffler_id,
					'cat_notification_type_id' => 9
				]);
			}
		});
	}
}
