<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Card extends Model
{
    protected $table = 'cards';         

    protected $fillable = [
        'alias',
        'number',
        'token',
        'type',
        'source_id',
        'active',
    ];

    protected $appends = [
        'number_formatted',
        'type_formatted'
    ];

    protected $hidden = [
        'token', 'source_id',
    ];

    public function getNumberFormattedAttribute()
    {
        return "**** $this->number";
    }

    public function getTypeFormattedAttribute()
    {
        if ($this->type == 'mastercard') {
            return 'MasterCard';
        }

        return ucfirst($this->type);
    }

    public function scopeActive($query)
    {
        return $query->where('active',true);
    }
}
