<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;

class Address extends Model
{
    protected $table = 'addresses';         

    protected $fillable = [
        'name',
        'country',
        'code_area',
        'state',
        'city',
        'cp',
        'phone',
        'contact_name',
        'user_id',
    ];

    public function scopeActive($query)
    {
        return $query->where('active',true);
    }
}
