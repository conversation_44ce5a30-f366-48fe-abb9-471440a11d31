<?php

namespace App\Model;

use Illuminate\Database\Eloquent\Model;
use App\User;

class Balance extends Model
{
    protected $table = 'balances';
	
	protected $with = ['raffle', 'user'];

    protected $fillable = [
        'amount',
        'points',
        'commission',
        'user_id',
        'card_id',
		'raffle_id',
		'description'
    ];

	public function raffle()
	{
		return $this->belongsTo(Raffle::class)->setEagerLoads([])->select(['id', 'name']);
	}

	public function user()
	{
		return $this->belongsTo(User::class)->setEagerLoads([])->select(['id', 'name']);
	}
}
