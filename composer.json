{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.2", "aws/aws-sdk-php": "^3.48", "awobaz/compoships": "^1.1", "barryvdh/laravel-dompdf": "^0.8.5", "brozot/laravel-fcm": "^1.3", "conekta/conekta-php": "^4.3", "darkaonline/l5-swagger": "6.*", "doctrine/dbal": "^2.10", "fideloper/proxy": "^4.0", "laravel/framework": "^6.2", "laravel/passport": "7.5.*", "laravel/tinker": "^1.0", "lcobucci/jwt": "3.3.3", "league/flysystem-aws-s3-v3": "^1.0", "maatwebsite/excel": "^3.1", "spatie/laravel-permission": "^3.2"}, "require-dev": {"facade/ignition": "^1.4", "fzaninotto/faker": "^1.4", "laravel/ui": "^1.1", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^8.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}