<html>
<head>
    <style>
    	
		.page-break {
		    page-break-after: always;
		}

		html {
			font-family: sans-serif;
			-webkit-text-size-adjust: 100%;
			-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
			margin: 0px 0px 0px 0px;
			padding: 0px 0px 0px 0px;
		}

		.left-first-page{
			width: 20%;
			float: left;
			margin: 0px 0px 0px 0px;
			padding: 0px 0px 0px 0px;
		}

		.right-first-page{
			margin: 0px 0px 0px 0px;
			padding: 0px 0px 0px 0px;
			width: 80%;
			float: right;
		}

		.right-label{
			width: 40%;
			float: right;
			padding-right: 0px;
		}

		.background-probeis{
			background-color: #591027;
			color: white;
			padding: 20px;
			border-radius: 0px 7px 7px 0px;
			-webkit-box-shadow: 10px 10px 0px 0px rgba(240,176,120,1);
			-moz-box-shadow: 10px 10px 0px 0px rgba(240,176,120,1);
			box-shadow: 10px 10px 0px 0px rgba(240,176,120,1);
		}

		.text-color-probeis{
			color: #591027;
		}

		.probeis-center-image{
			padding-left: 160px;
			width: 60%;
		}

		.footer-first {
            position: absolute;
            bottom: 1cm;
            left: 0cm;
            right: 0cm;
            height: 4cm;
            background-color: #BC955C;
            color: white;
            text-align: center;
            line-height: 35px;
            margin: 0px 0px 0px 0px;
			padding: 10px 0px 0px 0px;
			z-index: 9;
        }

        .img-first {
            position: absolute;
            bottom: -1cm;
            left: 2cm;
            right: 12cm;
            height: 30.7cm;
            background-color: #691C32;
			z-index: 10;

        }

        .img-background-first {
        	position: absolute;
            width: 100%;
            height: 100%;
			z-index: 0;
        }

        .text-footer-first {
        	color:white;
        	padding-left:50%;
        }

        .text-footer-card {
        	font-weight: bold;
        	font-size: 24px;
        	padding-top: 10%;
        }

        .text-footer-position {
        	font-weight: bold;
        	font-size: 18px;
        }

        .text-footer-name {
        	font-weight: bold;
        	font-size: 20px;
        }

        .logo{
        	position: absolute;
        	text-align: center;
        	padding-top: 1cm;
        	padding-left: 1.1cm;
        	width: 5cm;
        }

        .img-bandera{
        	position: absolute;
        	width: 7cm;
        	top:11cm;
        	bottom: 0cm;
        }

        .title-description{
        	width: 90%;
			float: left;
			padding-right: 0px;
			color:white;
			padding-top: .7cm;
			padding-left: 1cm;
        }

        .page-padding{
        	padding-right: 2cm;
        	padding-left: 2cm;
        	padding-top: 1.5cm;
        }

        .title-test{
        	background: #F0C78B;
        	text-align: center;
        	border-radius: 5px;
        	padding: 5px;
        }

        .justify{
        	text-align: justify;
        }

        .player-info{
        	background: #592232;
        	bottom: -1cm;
        	height: 22.2cm;
        	color: white;
        	border-radius: 3.5cm 3.5cm 0cm 0cm;
        	z-index: -10 !important;
        }

        .player-info-left{
        	position: absolute;
        	left: 2cm;
        	width: 40%;
        }

        .player-info-right{
        	position: absolute;
        	right: 2cm;
        	width: 55%;
        }

        .player-title-background{
        	font-size: 20px;
        	font-weight: bold;
        	color: white;
        	background-color: #592232;
        	border-radius: 10px 10px 0px 0px;
        	width: 4cm;
        	padding: 10px;
        	text-align: center;
        	margin-top: 20px;
        }

        .player-result-background{
        	font-size: 48px;
        	font-weight: bold;
        	color: #592232;
        	background-color: #F0C78B;
        	border-radius: 0px 0px 10px 10px;
        	width: 4cm;
        	height: 2cm;
        	padding-left: 10px;
        	padding-right: 10px;
        	padding-top: 5px;
        	padding-bottom: 5px;
        	text-align: center;
        }

        .player-unit-text{
        	color: #592232;
        	font-size: 18px;
        	font-weight: bold;
        	padding-left: 100px;
        }

        .error-title{
        	background-color: #F0C78B;
        	border-radius: 10px;
        	text-align: right;
        	font-size: 24px;
        }

        .error-number{
        	background-color: #592232;
        	color: white;
        	font-weight: bold;
        	border-radius: 0px 5px 5px 0px;
        	font-size: 18px;
        	padding-left: 2cm;
        	padding-right: 5px;
        	padding-top: 10px;
        	padding-bottom: 10px;
        	margin-top: 1cm;
        	width: 23%;
        }

        .test-photo{
        	border: 5px solid #592232;
        	border-radius: 10px;
        	position: absolute;
        	right: 1cm;
        	top: 1cm;
        	width: 200px;
        }

        .padding-error{
        	padding-right: 2cm;
        	padding-left: 2cm;
        	text-align: justify;
        }

        .background-objective{
        	position: absolute;
        	width: 80%;
        	z-index: -1;
        }

        .decoration{
        	position: absolute;
        	top: 0cm;
        	right: 1cm;
        	width: 3cm;
        }

        .cicle_gray{
        	position: absolute;
        	left: 0cm;
        	top: 0cm;
        	height: 17cm;
        	z-index: -2 !important;
        }

        .avatar{
        	width: 5cm;
        	height: 5cm;
        	border-radius: 2.5cm;
        	margin-bottom: 1cm;
        	margin-left: 1cm;
        	margin-top: 1cm;
        	z-index: 0 !important;
        }

        .avatar-background{
        	position: absolute;
        	width: 7cm;
        	height: 3.5cm;
        	z-index: -1 !important;
        }

        .background-error-image{
        	position: absolute;
        	width: 15cm;
        }

    </style>
</head>
<body>
	<div><img class="img-background-first" src="{{ public_path('img/cover.jpg') }}"></div>
	<div class="img-first">
		<img class="logo" src="{{ public_path('img/probeis.png') }}">
		<img class="img-bandera" src="{{ public_path('img/bandera.png') }}">
	</div>
	<div class="footer-first">
		<span class="text-footer-first text-footer-card">FICHA TÉCNICA DE JUGADOR</span><br>
		<span class="text-footer-first text-footer-position">{{ $player->principal_position->name }}</span><br>
		<span class="text-footer-first text-footer-name ">{{ $player->fullName }}</span><br>
	</div>
	<div class="page-break"></div>
	<div>
		<h1 class="title-description">DESCRIPCIÓN DE FICHA TÉCNICA</h1>
		<img class="background-objective" src="{{ public_path('img/background1.png') }}">
	</div>
	<div class="page-padding">
		<h2 class="text-color-probeis" style="margin-top: 2.5cm">OBJETIVO DE LA FICHA TÉCNICA</h2>
		<p class="text-color-probeis justify">Las siguientes condiciones enmarcan este deporte ya que está definido como deporte de fuerza explosiva y de ahí, estas mediciones nos dicen qué debemos hacer con su programa de entrenamiento además de poder predecir y proyectar al atleta de acuerdo a estos parámetros. Estas pruebas y análisis, nos permite realizar un seguimiento del atleta durante su carrera, detectar errores, origen y aplicar soluciones.</p>
		@foreach($tests as $test)
		    <h2 class="title-test text-color-probeis"> {{ $test->name }}</h2>
		    <p class="text-color-probeis justify"> {{ $test->description }}</p>
	    @endforeach
	</div>
	<div class="page-break"></div>
	<div class="page-padding">
		<div>
			<img src="{{ public_path('img/decoration.png') }}" class="decoration">
			<img src="{{ public_path('img/gray_circle.png') }}" class="cicle_gray" >
			<div class="player-info-left">
				<div>	
					<img src="{{ public_path('img/yellow_circle.png') }}" class="avatar-background">	
					<img src="{{$player->url_photo}}" class="avatar">
				</div>
				<div class="player-info">
					<div style="position:absolute; top: 2.5cm; margin-left: 0.5cm;">
						<label style="font-size: 28px;"><strong>ELEGIBILIDAD</strong></label><br>
						<label style="font-size: 32px;"><strong>{{ $player->elegibility }}</strong></label>
					</div>
					<div style="position:absolute; top: 5.5cm; margin-left: 2.5cm;">
						<label style="font-size: 28px;"><strong>EDAD</strong></label><br>
						<label style="font-size: 24px;"><strong>{{ $player->age }}</strong></label>
					</div>
					<div style="position:absolute; top: 8.5cm; margin-left: 1.5cm;">
						<label style="font-size: 28px;"><strong>NACIMIENTO</strong></label><br>
						<label style="font-size: 24px;"><strong>{{ $player->birthdate }}</strong></label>
					</div>
					<div style="position:absolute; top: 11.5cm; margin-left: 0.5cm;">
						<label style="font-size: 22px;"><strong>BRAZO</strong></label><br>
						<label style="font-size: 20px;"><strong>{{ $player->dominantHand ? $player->dominantHand->name : 'No disponible' }}</strong></label>
					</div>
					<div style="position:absolute; top: 11.5cm; margin-left: 4cm;">
						<label style="font-size: 22px;"><strong>BATEO</strong></label><br>
						<label style="font-size: 20px;"><strong>{{ $player->dominantHandBat ? $player->dominantHandBat->name : 'No disponible' }}</strong></label>
					</div>
					<div style="position:absolute; top: 13.5cm; margin-left: 0.5cm;">
						<label style="font-size: 22px;"><strong>ESTATURA</strong></label><br>
						<label style="font-size: 20px;"><strong>{{ $size }} CM</strong></label>
					</div>
					<div style="position:absolute; top: 13.5cm; margin-left: 4cm;">
						<label style="font-size: 22px;"><strong>PESO</strong></label><br>
						<label style="font-size: 20px;"><strong>{{ $weight }} KG</strong></label>
					</div>
					<div style="position:absolute; top: 15.5cm; margin-left: 0.5cm;">
						<label style="font-size: 22px;"><strong>% GRASA</strong></label><br>
						<label style="font-size: 20px;"><strong>{{ $percent }} %</strong></label>
					</div>
					<div style="position:absolute; top: 19cm; margin-left: 0.5cm;">
						<label style="font-size: 22px;"><strong>CONTACTO</strong></label><br>
						<label style="font-size: 20px;"><EMAIL></label>
					</div>
				</div>
			</div>
			<div class="player-info-right">
				<div>
					<div style="text-align: center;">
						<h1>{{ $player->principal_position->name }}</h1>
						@if( $player->principal_position->id == 1)
							<img src="{{ public_path('img/pitcher.png') }}" width="50px">
						@elseif( $player->principal_position->id == 2 )
							<img src="{{ public_path('img/catcher.png') }}" width="50px">
						@elseif( $player->principal_position->id == 3 )
							<img src="{{ public_path('img/infielder.png') }}" width="50px">
						@elseif( $player->principal_position->id == 4 )
							<img src="{{ public_path('img/outfielder.png') }}" width="50px">
						@endif
						<div style="margin-top: 30px;">
							<label style="font-size: 48px;" class="text-color-probeis">
								<strong>{{ $player->fullName }}</strong>
							</label>
						</div>
					</div>	
				</div>
				
				<div>
					<div style="display:inline-block;margin-top:5cm; margin-right: 20px;">
						<div>
							<div class="player-title-background"> 60 YARDAS </div>
							<div class="player-result-background">
								{{ $yards }}
								<div class="player-unit-text">SEG</div>
							</div>
							
						</div>
						<div>
							<div class="player-title-background"> SALTO </div>
							<div class="player-result-background">
								{{ $jump }}
								<div class="player-unit-text">PIES</div>
							</div>
							
						</div>
						<div>
							<div class="player-title-background"> V BATEO </div>
							<div class="player-result-background">
								{{ $batv }}
								<div class="player-unit-text">MPH</div>
							</div>
						</div>
						<div>
							<div class="player-title-background"> P BRAZO </div>
							<div class="player-result-background">
								{{ $arm }}
								<div class="player-unit-text">MPH</div>
							</div>
						</div>
					</div>
					<div style="display:inline-block;margin-top:-3.2cm;">
						<div>
							<div class="player-title-background"> 5-10-5 </div>
							<div class="player-result-background">
								{{ $five }}
								<div class="player-unit-text">SEG</div>
							</div>
						</div>
						<div>
							<div class="player-title-background"> FLEXIBILIDAD </div>
							<div class="player-result-background">
								{{ $flex }}
								<div class="player-unit-text">CM</div>
							</div>
						</div>
						<div>
							<div class="player-title-background"> H BATEO </div>
							<div class="player-result-background">
								{{ $bath }}
								<div class="player-unit-text">20-80</div>
							</div>
						</div>
						<div>
							<div class="player-title-background"> H DEFENSIVA </div>
							<div class="player-result-background">
								{{ $defense }}
								<div class="player-unit-text">20-80</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
    
	<!--div class="page-break"></div>
	<div class="page-padding">
		<h2 class="text-color-probeis">Resultados:</h2>
		<div>
			<table width="100%">
			    <tr>
			      <th class="background-probeis" width="40%" style="padding: 10px">Prueba</th>
			      <th class="background-probeis" width="20%" style="padding: 10px">Resultado</th>
			      <th class="background-probeis" width="40%" style="padding: 10px">Comentarios</th>
			    </tr>
			    @foreach($results as $result)
				    <tr>
				      <td>{{ $result->test->name }} </td>
				      <td>{{ $result->result }} </td>
				      <td>{{ $result->comments }} </td>
				    </tr>
			    @endforeach
			  </table>
		</div>
	</div-->
	<div class="page-break"></div>
	@foreach($positions as $position)
		@if($position->errors && count($position->errors) > 0)
			<div>
				<div class="page-padding">
					<div>
						<img src="{{ public_path('img/background_error.png') }}" class="background-error-image">
						<h1 class="text-color-probeis" 
							style="padding-left: 120px;z-index:10;
							width: 350px;">
							DIAGNÓSTICO DE {{ $position->name }}
						</h1>
					</div>
					<div>
						<img src="{{$player->url_photo}}" class="test-photo">
					</div>
				</div>
				<div style="margin-top:50px;">
					@foreach($position->errors as $error)
					  	<div>
							<div class="error-number"> ERROR NÚMERO {{ $error->error->number }}</div>
							<div class="padding-error">
								<p class="text-color-probeis">
									{{ $error->error->error }}
								</p>
								<p class="text-color-probeis">
									<strong>ORIGEN:</strong> <br>
									{{ $error->error->origin }}
								</p>
								<p class="text-color-probeis">
									<strong>SOLUCIÓN:</strong> <br>
									{{ $error->error->solution }}
								</p> 
							</div>		
					  	</div>
					  	@if( 
					  		($loop->index%2 != 0 || $loop->last) 
					  		&& (!$loop->parent->last) )
					  		<div class="page-break"></div>
					  	@elseif( $loop->parent->last && $loop->index%2 != 0 && !$loop->last)
					  		<div class="page-break"></div>
					  	@endif	
					@endforeach 
				</div>
			</div> 
		@endif
	@endforeach
	@if($photos)
		<div class="page-break"></div>
		@foreach($photos as $test)
			<div class="padding-error">
				<h2 class="title-test text-color-probeis"> {{ $test->name }}</h2>
				@foreach($test->photos as $photo)
					<div style="text-align: center;">
						<img style="margin-top: 30px;" src="{{ $photo->url_photo }}" height="450px">
					</div>
				@endforeach
			</div>
		@endforeach
	@endif 
</body>
</html>
