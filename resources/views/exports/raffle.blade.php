<table>
    <tbody>
        <tr>
            <td colspan="22" style="font-size:24px;">
                Rifas
            </td>
        </tr>
        <tr> 
            <td style="background-color:#6c757d;color:#FFFFFF;">#</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Nombre rifa</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Descripción de la rifa</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">ID Rifador</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Rifador</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">RFC rifador</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Estado</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Ciudad</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Tipo de premio</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Tipo de producto</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Fecha de sorteo</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Valor del premio</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Total de boletos</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Precio del boleto</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Boletos vendidos</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Cancelada</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Total de compras</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Compras rafiki</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Compras conekta</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Comisiones conekta</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Promedio de boletos por orden</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Estatus de pago</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Monto generado</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">Comisión rafiki</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">IVA comisión</td>
            <td style="background-color:#6c757d;color:#FFFFFF;">ISR</td>
        </tr>
        @foreach($data as $raffle)
            <tr> 
                <td style="width: 10px;"> {{ $raffle->id }}</td>
                <td style="width: 30px;"> {{ $raffle->name }}</td>
                <td style="width: 30px;"> {{ $raffle->description }}</td>
                <td style="width: 10px;"> {{ $raffle->user_id }}</td>
                <td style="width: 10px;"> {{ $raffle->nickname }}</td>
                <td style="width: 10px;"> {{ $raffle->rfc }}</td>
                <td style="width: 10px;"> {{ $raffle->state }}</td>
                <td style="width: 10px;"> {{ $raffle->city }}</td>
                <td style="width: 10px;"> 
                    @if($raffle->prize_type == 'New')
                        Nuevo
                    @elseif($raffle->prize_type == 'Used')
                        Usado
                    @endif
                </td>
                <td style="width: 10px;">
                	@if($raffle->product_type == 'Physical')
                		Fisico
                	@elseif($raffle->product_type == 'Digital')
                        Digital
                	@elseif($raffle->product_type == 'Service')
                        Service
                	@endif
                </td>
                <td style="width: 10px;"> {{ $raffle->finish }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->prize_value, 2)  }}</td>
                <td style="width: 10px;"> {{ $raffle->num_tickets }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->ticket_price, 2) }}</td>
                <td style="width: 10px;"> {{ $raffle->sold_tickets }}</td>
                <td style="width: 10px;"> {{ $raffle->canceled }}</td>
                <td style="width: 10px;"> {{ $raffle->total_orders }}</td>
                <td style="width: 10px;"> {{ $raffle->rafiki_orders }}</td>
                <td style="width: 10px;"> {{ $raffle->conekta_orders }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->conekta_fees, 2)  }}</td>
                <td style="width: 10px;"> {{ $raffle->avg_ticket_order }}</td>
                <td style="width: 10px;">
                    @if($raffle->payment_status == 'pending')
                        Pendiente
                    @elseif($raffle->payment_status == 'accepted')
                        Realizado
                    @elseif($raffle->payment_status == 'not_generated')
                        No generado
                    @endif
                </td>
                <td style="width: 10px;"> ${{ number_format($raffle->amount, 2) }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->fee_amount, 2)  }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->fee_taxes, 2)  }}</td>
                <td style="width: 10px;"> ${{ number_format($raffle->isr_taxes, 2)  }}</td>
            </tr>
        @endforeach
        <tr>
            <td colspan="22">
                Fecha de creación: {{ $date }}
            </td>
        </tr>
    </tbody>
</table>
