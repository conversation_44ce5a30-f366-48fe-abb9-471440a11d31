<table>
    <tbody>
        <tr>
            <td colspan="7" style="font-size:24px;">
                Sede:  {{ $campus->name }} 
            </td>
        </tr>
            <tr> 
                <td colspan="2" style="background-color:#a42244;color:#FFFFFF;">Nombre</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Fecha de nacimiento</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Edad</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Genero</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Estado</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Ciudad</td>
                <td style="background-color:#a42244;color:#FFFFFF;">CP</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Llegó acompañado?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Le gustan los deportes?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Practicas algún deporte?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Sabe de beisbol?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Juega beisbol?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Donde juega?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Telefono</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Correo electronico</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Aprendío algo nuevo?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Qué actividad le gusto mas?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Quiere hacer algún comentario?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Te gustaría aprender mas de béisbol?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Le gustaría jugar béisbol?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Desea ingresar a una academia?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">¿Le gustó su experiencia?</td>
                <td style="background-color:#a42244;color:#FFFFFF;">Calificación</td>
                
            </tr>
            @foreach($players as $player)
                <tr> 
                    <td style="width: 40px;" colspan="2" > {{ $player->name }} </td>
                    <td style="width: 20px;">{{ $player->birthdate}}</td>
                    <td style="width: 20px;">{{ $player->age}}</td>
                    <td style="width: 20px;">{{ $player->gender ? $player->gender->name  : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->state ? $player->state->name : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->city }}</td>
                    <td style="width: 20px;">{{ $player->cp }}</td>
                    <td style="width: 20px;">{{ $player->is_accompanied == 1 ? 'Si' : 'No' }}</td>
                    <td style="width: 20px;">{{ $player->like_sport == 1 ? 'Si' : 'No' }}</td>
                    <td style="width: 20px;">{{ $player->sport  ? $player->sport->name : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->know_baseball == 1 ? 'Si' : 'No' }}</td>
                    <td style="width: 20px;">{{ $player->play_baseball == 1 ? 'Si' : 'No' }}</td>
                    <td style="width: 20px;">{{ $player->type_play ? ($player->type_play->name == 1 ? 'Si' : 'No') : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->phone }}</td>
                    <td style="width: 20px;">{{ $player->email }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->learned == 1 ? 'Si' : 'No') : 'No disponible'}}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->activity ? $player->second->activity->name : 'No disponible') : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->comment) : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->want_learn_baseball == 1 ? 'Si' : 'No') : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->want_play_baseball == 1 ? 'Si' : 'No') : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->want_academy == 1 ? 'Si' : 'No') : 'No disponible' }}</td>


                    <td style="width: 20px;">{{ $player->second ? ($player->second->liked == 1 ? 'Si' : 'No') : 'No disponible' }}</td>
                    <td style="width: 20px;">{{ $player->second ? ($player->second->qualification) : 'No disponible' }}</td>
                </tr>
            @endforeach
        <tr>
            <td colspan="5">
                Fecha reporte: {{ $date }}
            </td>
        </tr>
    </tbody>
</table>