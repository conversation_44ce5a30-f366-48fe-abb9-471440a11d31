<?php return array (
  'codeToName' => 
  array (
    13 => 'CR',
    32 => 'space',
    33 => 'exclam',
    34 => 'quotedbl',
    35 => 'numbersign',
    36 => 'dollar',
    37 => 'percent',
    38 => 'ampersand',
    39 => 'quotesingle',
    40 => 'parenleft',
    41 => 'parenright',
    42 => 'asterisk',
    43 => 'plus',
    44 => 'comma',
    45 => 'hyphen',
    46 => 'period',
    47 => 'slash',
    48 => 'zero',
    49 => 'one',
    50 => 'two',
    51 => 'three',
    52 => 'four',
    53 => 'five',
    54 => 'six',
    55 => 'seven',
    56 => 'eight',
    57 => 'nine',
    58 => 'colon',
    59 => 'semicolon',
    60 => 'less',
    61 => 'equal',
    62 => 'greater',
    63 => 'question',
    64 => 'at',
    65 => 'A',
    66 => 'B',
    67 => 'C',
    68 => 'D',
    69 => 'E',
    70 => 'F',
    71 => 'G',
    72 => 'H',
    73 => 'I',
    74 => 'J',
    75 => 'K',
    76 => 'L',
    77 => 'M',
    78 => 'N',
    79 => 'O',
    80 => 'P',
    81 => 'Q',
    82 => 'R',
    83 => 'S',
    84 => 'T',
    85 => 'U',
    86 => 'V',
    87 => 'W',
    88 => 'X',
    89 => 'Y',
    90 => 'Z',
    91 => 'bracketleft',
    92 => 'backslash',
    93 => 'bracketright',
    94 => 'asciicircum',
    95 => 'underscore',
    96 => 'grave',
    97 => 'a',
    98 => 'b',
    99 => 'c',
    100 => 'd',
    101 => 'e',
    102 => 'f',
    103 => 'g',
    104 => 'h',
    105 => 'i',
    106 => 'j',
    107 => 'k',
    108 => 'l',
    109 => 'm',
    110 => 'n',
    111 => 'o',
    112 => 'p',
    113 => 'q',
    114 => 'r',
    115 => 's',
    116 => 't',
    117 => 'u',
    118 => 'v',
    119 => 'w',
    120 => 'x',
    121 => 'y',
    122 => 'z',
    123 => 'braceleft',
    124 => 'bar',
    125 => 'braceright',
    126 => 'asciitilde',
    161 => 'exclamdown',
    162 => 'cent',
    163 => 'sterling',
    164 => 'currency',
    165 => 'yen',
    166 => 'brokenbar',
    167 => 'section',
    168 => 'dieresis',
    169 => 'copyright',
    170 => 'ordfeminine',
    171 => 'guillemotleft',
    172 => 'logicalnot',
    174 => 'registered',
    175 => 'macron',
    176 => 'degree',
    177 => 'plusminus',
    180 => 'acute',
    182 => 'paragraph',
    183 => 'periodcentered',
    184 => 'cedilla',
    186 => 'ordmasculine',
    187 => 'guillemotright',
    188 => 'onequarter',
    189 => 'onehalf',
    190 => 'threequarters',
    191 => 'questiondown',
    192 => 'Agrave',
    193 => 'Aacute',
    194 => 'Acircumflex',
    195 => 'Atilde',
    196 => 'Adieresis',
    197 => 'Aring',
    198 => 'AE',
    199 => 'Ccedilla',
    200 => 'Egrave',
    201 => 'Eacute',
    202 => 'Ecircumflex',
    203 => 'Edieresis',
    204 => 'Igrave',
    205 => 'Iacute',
    206 => 'Icircumflex',
    207 => 'Idieresis',
    208 => 'Eth',
    209 => 'Ntilde',
    210 => 'Ograve',
    211 => 'Oacute',
    212 => 'Ocircumflex',
    213 => 'Otilde',
    214 => 'Odieresis',
    215 => 'multiply',
    216 => 'Oslash',
    217 => 'Ugrave',
    218 => 'Uacute',
    219 => 'Ucircumflex',
    220 => 'Udieresis',
    221 => 'Yacute',
    222 => 'Thorn',
    223 => 'germandbls',
    224 => 'agrave',
    225 => 'aacute',
    226 => 'acircumflex',
    227 => 'atilde',
    228 => 'adieresis',
    229 => 'aring',
    230 => 'ae',
    231 => 'ccedilla',
    232 => 'egrave',
    233 => 'eacute',
    234 => 'ecircumflex',
    235 => 'edieresis',
    236 => 'igrave',
    237 => 'iacute',
    238 => 'icircumflex',
    239 => 'idieresis',
    240 => 'eth',
    241 => 'ntilde',
    242 => 'ograve',
    243 => 'oacute',
    244 => 'ocircumflex',
    245 => 'otilde',
    246 => 'odieresis',
    247 => 'divide',
    248 => 'oslash',
    249 => 'ugrave',
    250 => 'uacute',
    251 => 'ucircumflex',
    252 => 'udieresis',
    253 => 'yacute',
    254 => 'thorn',
    255 => 'ydieresis',
    305 => 'dotlessi',
    338 => 'OE',
    339 => 'oe',
    710 => 'circumflex',
    730 => 'ring',
    732 => 'tilde',
    8211 => 'endash',
    8212 => 'emdash',
    8216 => 'quoteleft',
    8217 => 'quoteright',
    8218 => 'quotesinglbase',
    8220 => 'quotedblleft',
    8221 => 'quotedblright',
    8222 => 'quotedblbase',
    8226 => 'bullet',
    8230 => 'ellipsis',
    8242 => 'minute',
    8243 => 'second',
    8249 => 'guilsinglleft',
    8250 => 'guilsinglright',
    8260 => 'fraction',
    8364 => 'Euro',
    8482 => 'trademark',
    8722 => 'minus',
  ),
  'isUnicode' => true,
  'EncodingScheme' => 'FontSpecific',
  'FontName' => 'Nunito',
  'FullName' => 'Nunito Regular',
  'Version' => 'Version 3.504',
  'PostScriptName' => 'Nunito-Regular',
  'Weight' => 'Medium',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'UnderlineThickness' => '50',
  'UnderlinePosition' => '-75',
  'FontHeightOffset' => '0',
  'Ascender' => '1011',
  'Descender' => '-353',
  'FontBBox' => 
  array (
    0 => '-412',
    1 => '-275',
    2 => '1285',
    3 => '1081',
  ),
  'StartCharMetrics' => '267',
  'C' => 
  array (
    0 => 0.0,
    13 => 258.0,
    32 => 258.0,
    33 => 229.0,
    34 => 421.0,
    35 => 600.0,
    36 => 600.0,
    37 => 930.0,
    38 => 664.0,
    39 => 221.0,
    40 => 314.0,
    41 => 314.0,
    42 => 450.0,
    43 => 600.0,
    44 => 229.0,
    45 => 425.0,
    46 => 229.0,
    47 => 283.0,
    48 => 600.0,
    49 => 600.0,
    50 => 600.0,
    51 => 600.0,
    52 => 600.0,
    53 => 600.0,
    54 => 599.0,
    55 => 600.0,
    56 => 600.0,
    57 => 600.0,
    58 => 229.0,
    59 => 229.0,
    60 => 600.0,
    61 => 600.0,
    62 => 600.0,
    63 => 443.0,
    64 => 926.0,
    65 => 727.0,
    66 => 674.0,
    67 => 677.0,
    68 => 739.0,
    69 => 582.0,
    70 => 557.0,
    71 => 724.0,
    72 => 758.0,
    73 => 256.0,
    74 => 326.0,
    75 => 628.0,
    76 => 540.0,
    77 => 841.0,
    78 => 739.0,
    79 => 764.0,
    80 => 645.0,
    81 => 764.0,
    82 => 686.0,
    83 => 617.0,
    84 => 598.0,
    85 => 724.0,
    86 => 691.0,
    87 => 1101.0,
    88 => 646.0,
    89 => 601.0,
    90 => 590.0,
    91 => 314.0,
    92 => 282.0,
    93 => 314.0,
    94 => 600.0,
    95 => 500.0,
    96 => 300.0,
    97 => 530.0,
    98 => 582.0,
    99 => 479.0,
    100 => 582.0,
    101 => 527.0,
    102 => 328.0,
    103 => 586.0,
    104 => 568.0,
    105 => 232.0,
    106 => 232.0,
    107 => 500.0,
    108 => 232.0,
    109 => 852.0,
    110 => 568.0,
    111 => 555.0,
    112 => 582.0,
    113 => 582.0,
    114 => 353.0,
    115 => 480.0,
    116 => 340.0,
    117 => 561.0,
    118 => 514.0,
    119 => 842.0,
    120 => 518.0,
    121 => 514.0,
    122 => 503.0,
    123 => 351.0,
    124 => 261.0,
    125 => 351.0,
    126 => 600.0,
    160 => 258.0,
    161 => 229.0,
    162 => 600.0,
    163 => 600.0,
    164 => 600.0,
    165 => 600.0,
    166 => 261.0,
    167 => 537.0,
    168 => 300.0,
    169 => 815.0,
    170 => 330.0,
    171 => 441.0,
    172 => 600.0,
    173 => 425.0,
    174 => 815.0,
    175 => 300.0,
    176 => 365.0,
    177 => 600.0,
    178 => 380.0,
    179 => 380.0,
    180 => 300.0,
    181 => 561.0,
    182 => 582.0,
    183 => 229.0,
    184 => 300.0,
    185 => 380.0,
    186 => 342.0,
    187 => 439.0,
    188 => 933.0,
    189 => 933.0,
    190 => 933.0,
    191 => 443.0,
    192 => 727.0,
    193 => 727.0,
    194 => 727.0,
    195 => 727.0,
    196 => 727.0,
    197 => 727.0,
    198 => 991.0,
    199 => 677.0,
    200 => 582.0,
    201 => 582.0,
    202 => 582.0,
    203 => 582.0,
    204 => 256.0,
    205 => 256.0,
    206 => 256.0,
    207 => 256.0,
    208 => 752.0,
    209 => 739.0,
    210 => 764.0,
    211 => 764.0,
    212 => 764.0,
    213 => 764.0,
    214 => 764.0,
    215 => 600.0,
    216 => 764.0,
    217 => 724.0,
    218 => 724.0,
    219 => 724.0,
    220 => 724.0,
    221 => 601.0,
    222 => 645.0,
    223 => 616.0,
    224 => 530.0,
    225 => 530.0,
    226 => 530.0,
    227 => 530.0,
    228 => 530.0,
    229 => 530.0,
    230 => 853.0,
    231 => 479.0,
    232 => 527.0,
    233 => 527.0,
    234 => 527.0,
    235 => 527.0,
    236 => 232.0,
    237 => 232.0,
    238 => 232.0,
    239 => 232.0,
    240 => 559.0,
    241 => 568.0,
    242 => 555.0,
    243 => 555.0,
    244 => 555.0,
    245 => 555.0,
    246 => 555.0,
    247 => 600.0,
    248 => 555.0,
    249 => 561.0,
    250 => 561.0,
    251 => 561.0,
    252 => 561.0,
    253 => 514.0,
    254 => 582.0,
    255 => 514.0,
    305 => 232.0,
    338 => 1045.0,
    339 => 904.0,
    699 => 300.0,
    700 => 300.0,
    710 => 300.0,
    730 => 300.0,
    732 => 300.0,
    8201 => 200.0,
    8203 => 0.0,
    8211 => 500.0,
    8212 => 1000.0,
    8216 => 229.0,
    8217 => 229.0,
    8218 => 229.0,
    8220 => 395.0,
    8221 => 395.0,
    8222 => 395.0,
    8226 => 524.0,
    8230 => 687.0,
    8242 => 221.0,
    8243 => 421.0,
    8249 => 260.0,
    8250 => 258.0,
    8260 => 173.0,
    8308 => 380.0,
    8364 => 600.0,
    8482 => 915.0,
    8722 => 600.0,
    8725 => 600.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJzt1FUPHkUYhuH7a0sNd3d3d4K7O4Hg7u4c4foncHcnuLu7u/svoCE9aJoWGiiUhOtKZmZ3dvfdZ+ZgGjQxQyb6ZFxDu6W7uq03+qKvuruHe6Rre7eberzbu7ezO6dzO6/zu6ALu6iLu6Qbu6eP+7CPuqMvG9Y0Tdv0zdhszd4czdl8zd8CLdhCLdIyLdfyrdCKrdTqrdGardU6Pdh1PdTP3TcY0bpt2mZt0VZt3w7t2E7t0Z7t1d7t034d1hEd2VEd07Gd1Mmd0qmd0f392AN9Okkr/GMvd2uv9Vav93Y/9c1geN92Zk/2WU/03WBkP/RJl3X5YGif93XXD4Z1aWf1VFd3Vdd0ZyObquGNbkSjmrrpmrWZmrlZmre5mrt5mqGFW6JFW6ylWrz3W7LVWrlVWrW1W7aj27D1Wr+N26CN2qTN266t26Zt271d2rXd2rJ9O6j9O6BDOrAPOrgTO67jO6HTOrzTJ8MO/Jmd/3aFpTt0MuSA/4jBkMEEzt3BVJP49agxbfTkTQQAAAAAAAAAAAAAAAAAwH/WK2Paq+PNPdajY69e6MVeGjM+1/M9O3buhrHjzROp+Eu/jnP3dM9M4J0r/1LWSXfFP1x/fG/+y/+bkr6f0gH4R733e//OFE4BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPxP/AbfBWM6',
  '_version_' => 6,
);