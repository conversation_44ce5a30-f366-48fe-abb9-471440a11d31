{"openapi": "3.0.0", "info": {"title": "<PERSON><PERSON><PERSON>", "version": "0.1"}, "paths": {"/address": {"get": {"tags": ["Direcciones"], "summary": "Listado de direcciones del usuario", "description": "Listado de direcciones del usuario", "operationId": "790fdd25a63a3a81d2e4d0009218e3fa", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Address"}}}}}}}, "post": {"tags": ["Direcciones"], "summary": "Guardar una rifa", "description": "Guardar una rifa", "operationId": "d147bdca0bba9a5aefa8097adfef4971", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}}}, "security": [{"bearerAuth": []}]}}, "/address/{id}": {"get": {"tags": ["Direcciones"], "summary": "Obtener los datos de una dirección", "description": "Obtener los datos de una dirección", "operationId": "6f3c21f11460bcc1ed31a13041e4e240", "parameters": [{"name": "id", "in": "path", "description": "Id de la dirección", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Direcciones"], "summary": "Guardar una rifa", "description": "Guardar una rifa", "operationId": "d07784f4e881ae3a22ec83f671ff8c01", "parameters": [{"name": "id", "in": "path", "description": "Id de la dirección", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Direcciones"], "summary": "Guardar una rifa", "description": "Guardar una rifa", "operationId": "b22408dc245e130de9ec36e3572e612c", "parameters": [{"name": "id", "in": "path", "description": "Id de la dirección", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Address"}}}}}, "security": [{"bearerAuth": []}]}}, "/application": {"get": {"tags": ["Solicitudes"], "summary": "Listado de solicitudes", "description": "Listado de solicitudes", "operationId": "5bbbfa1be76db8c93fcaf4e5676eaff2", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Application"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Solicitudes"], "summary": "Guardar una solicitud", "description": "Guardar una solicitud", "operationId": "10b1f5823a2602fa4c4ef155b9a044ff", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/ApplicationSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Application"}}}}}, "security": [{"bearerAuth": []}]}}, "/application/{id}": {"get": {"tags": ["Solicitudes"], "summary": "Obtener la información de la solicitud", "description": "Obtener la información de la solicitud", "operationId": "53a449392d58bcdac6d371b2d6b466ab", "parameters": [{"name": "id", "in": "path", "description": "<PERSON><PERSON> de la solicitud", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Application"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Solicitudes"], "summary": "Actual<PERSON><PERSON> solicitud", "description": "Actual<PERSON><PERSON> solicitud", "operationId": "0c79a9f4b928cab4aac1c6ca6eac544b", "parameters": [{"name": "id", "in": "path", "description": "<PERSON><PERSON> de la solicitud", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationEdit"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Application"}}}}}, "security": [{"bearerAuth": []}]}}, "/application/{id}/rfc": {"post": {"tags": ["Solicitudes"], "summary": "Actual<PERSON><PERSON> solicitud", "description": "Actual<PERSON><PERSON> solicitud", "operationId": "480506643387d941188362f1fd1b5702", "parameters": [{"name": "id", "in": "path", "description": "<PERSON><PERSON> de la solicitud", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApplicationEditRFC"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Application"}}}}}, "security": [{"bearerAuth": []}]}}, "/application/status": {"get": {"tags": ["Solicitudes Estatus"], "summary": "Obtener  el estatus de la solicitud", "description": "Obtener  el estatus de la solicitud", "operationId": "bdf7a60709bc43b21ccf89e56926706e", "parameters": [{"name": "id", "in": "path", "description": "<PERSON><PERSON> de la solicitud", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Application"}}}}}}, "security": [{"bearerAuth": []}]}}, "/login": {"post": {"tags": ["Autenticación"], "summary": "Inicio de sesión", "description": "Inicio de sesión, los campos requeridos varian dependiendo del metodo para iniciar sesión, si se quiere ingresar por password es necesario ingresar con 'email' y 'password'. Si se va a iniciar con facebook es necesario enviar 'email', 'facebook_id', 'name' y 'last_name', en este caso si el usuario no esta registrado se hará el registro en automatico.", "operationId": "ec9c379d2a959de66404708dd4a9322d", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"password": {"type": "string"}, "email": {"type": "string"}, "facebook_id": {"description": "Login con facebook.", "type": "string"}, "apple_id": {"description": "Login con apple.", "type": "string"}, "url_image": {"description": "Login con facebook / apple. Imagen que se obtiene desde la información de facebook / apple, solo se usará si el usuario no estaba previamente registrado", "type": "string"}, "name": {"description": "Login con facebook / apple.", "type": "string"}, "last_name": {"description": "Login con facebook / apple.", "type": "string"}, "token": {"description": "Login desde moviles.", "type": "string"}, "type": {"description": "Login desde moviles.", "type": "string", "example": "ios:android"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPermissions"}}}}}}}, "/admin/login": {"post": {"tags": ["Autenticación"], "summary": "Inicio de sesión", "description": "Inicio de sesión, los campos requeridos varian dependiendo del metodo para iniciar sesión, si se quiere ingresar por password es necesario ingresar con 'email' y 'password'. Si se va a iniciar con facebook es necesario enviar 'email', 'facebook_id', 'name' y 'last_name', en este caso si el usuario no esta registrado se hará el registro en automatico.", "operationId": "b0905475048db54bd46472b229afbb97", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"password": {"type": "string"}, "email": {"type": "string"}, "facebook_id": {"description": "Login con facebook.", "type": "string"}, "apple_id": {"description": "Login con apple.", "type": "string"}, "url_photo": {"description": "Login con facebook / apple. Imagen que se obtiene desde la información de facebook / apple, solo se usará si el usuario no estaba previamente registrado", "type": "string"}, "name": {"description": "Login con facebook / apple.", "type": "string"}, "last_name": {"description": "Login con facebook / apple.", "type": "string"}, "token": {"description": "Login desde moviles.", "type": "string"}, "type": {"description": "Login desde moviles.", "type": "string", "example": "ios:android"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPermissions"}}}}}}}, "/app/logout": {"post": {"tags": ["Autenticación"], "summary": "Cierre de sesión", "description": "Cierre de sesión, se puede incluir los datos del dispositivo.", "operationId": "ac1498aef2205cf99d7a272ad221f1a6", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"token": {"description": "Login desde moviles.", "type": "string"}, "type": {"description": "Login desde moviles.", "type": "string", "example": "ios:android"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito"}}}}, "/reset-password": {"post": {"tags": ["Autenticación"], "summary": "Envía un correo al usuario para que pueda recuperar su contraseña.", "description": "Envía un correo al usuario para que pueda recuperar su contraseña.", "operationId": "116c1acd944029a819be9b27c6f9e4f1", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/reset-confirm": {"post": {"tags": ["Autenticación"], "summary": "Segundo paso de la recuperación de contraseña.", "description": "Segundo paso de la recuperación de contraseña.", "operationId": "f14e37b459aa69215fb4fda8fda0786f", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "token", "password", "password_confirmation"], "properties": {"email": {"type": "string"}, "token": {"type": "string"}, "password": {"type": "string"}, "password_confirmation": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/email/verify/{id}/{hash}": {"get": {"tags": ["Verify"], "summary": "Verifica un usuario", "description": "Verifica al usuario con la información que se envió a su correo.", "operationId": "2295166070a7e08a04399e54af513a63", "parameters": [{"name": "id", "in": "path", "description": "Id del usuario", "required": true}, {"name": "hash", "in": "path", "description": "Cadena para verificar el usuario", "required": true}, {"name": "expires", "in": "query", "description": "Timestap con la fecha en que expira la url", "required": true}, {"name": "signature", "in": "query", "description": "Firma generada con la información del usuario", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/email/resend": {"post": {"tags": ["Verify"], "summary": "Envía un correo de verificación", "description": "Envía un correo de verificación.", "operationId": "b91a622d7d4db17dd3d2cd9261101016", "parameters": [{"name": "id", "in": "query", "description": "Id del usuario", "required": true}, {"name": "signature", "in": "query", "description": "Firma generada con la información del usuario", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "boolean"}}}}}}}, "/wallet/deposit": {"post": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Deposito al monedero rafiki", "description": "Deposito al monedero rafiki", "operationId": "0dc0ef859897336e9dfd6df2b232f96c", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["card_id", "amount"], "properties": {"card_id": {"type": "string"}, "amount": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Balance"}}}}, "500": {"description": "Error al procesar el pago"}, "422": {"description": "Error en la petición"}}}}, "/wallet": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Obtener el estatus del monedero rafiki saldo, depositos y pagos", "description": "Obtener el estatus del monedero rafiki saldo, depositos y pagos", "operationId": "ccedc2c1e3ad5eea1350e044692af499", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Wallet"}}}}}}}, "/card": {"get": {"tags": ["Tarjetas"], "summary": "Listado de tarjetas", "description": "Listado de tarjetas", "operationId": "********************************", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Card"}}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Tarjetas"], "summary": "Actual<PERSON>r alias de una tarjeta", "description": "Actual<PERSON>r alias de una tarjeta", "operationId": "a656737d5c4f51d120db72e5d4cf49fb", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"alias": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Card"}}}}, "403": {"description": "El usuario que realizo la petición no es dueño de la tarjeta"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Tarjetas"], "summary": "Registro de una tarjeta", "description": "Registro de una tarjeta", "operationId": "90a4f2f74659c54f06eb5c184b5ffec0", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CardSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Card"}}}}}, "security": [{"bearerAuth": []}]}}, "/card/{id}": {"delete": {"tags": ["Tarjetas"], "summary": "Eliminar una tarjeta", "description": "Eliminar una tarjeta, solo la puede eliminar el usuario que posee esa tarjeta y no tenga una rifa activa", "operationId": "b9af849af16a3a318db75c37e6cd67d2", "parameters": [{"name": "id", "in": "path", "description": "<PERSON><PERSON> de la tarjeta", "required": true}], "responses": {"200": {"description": "Exito"}, "403": {"description": "El usuario que realizo la petición no es dueño de la tarjeta"}, "412": {"description": "Se tiene una rifa activa, no se puede borrar la tarjeta"}}, "security": [{"bearerAuth": []}]}}, "/catalog/state": {"get": {"tags": ["Catalogos"], "summary": "Listado de estados", "description": "Listado de estados", "operationId": "c461d25bd34f91af57ffd1b5af5c4269", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Catalog"}}}}}}}}, "/category": {"get": {"tags": ["Catalogos"], "summary": "Listado de categorias", "description": "Listado de categorias", "operationId": "062b0e17b0b265231ad33ece1785b1fe", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CatalogDescription"}}}}}}}}, "/category/{id}/subcategories": {"get": {"tags": ["Catalogos"], "summary": "Listado de subcategorias de una categoría", "description": "Listado de subcategorias de una categoría", "operationId": "74d9ea7b0afdc8fad82f7aa46603b587", "parameters": [{"name": "id", "in": "path", "description": "Id de la categoría", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Subcategory"}}}}}}}}, "/notification": {"get": {"tags": ["Notificaciones"], "summary": "Obtener las notificaciones no leídas de un usuario", "description": "Obtener las notificaciones no leídas de un usuario", "operationId": "374cf5759ae311f38666cfd58baeeee3", "parameters": [{"name": "page", "in": "path", "description": "Número de página", "required": false}, {"name": "size", "in": "path", "description": "Tamaño de la página", "required": false}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationPaginated"}}}}}}, "security": [{"bearerAuth": []}]}}, "/notification/show-all": {"get": {"tags": ["Notificaciones"], "summary": "Obtener las notificaciones", "description": "Obtener las notificaciones", "operationId": "84e6fe6d5ea300384f39b4c92f3f7ac1", "parameters": [{"name": "page", "in": "path", "description": "Número de página", "required": false}, {"name": "size", "in": "path", "description": "Tamaño de la página", "required": false}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationPaginated"}}}}}}, "security": [{"bearerAuth": []}]}}, "/notification/read/{id}": {"put": {"tags": ["Notificaciones"], "summary": "Marcar una notificación como leída", "description": "Marcar una notificación como leída", "operationId": "7513dc7ba9b2a9a4d4a4dda9adfa5cf1", "parameters": [{"name": "id", "in": "path", "description": "Id de la notificación", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}, "security": [{"bearerAuth": []}]}}, "/notification/read-all": {"put": {"tags": ["Notificaciones"], "summary": "Marcar todas las notificaciones del usuario como leídas", "description": "Marcar todas las notificaciones del usuario como leídas", "operationId": "8bc25d2cfa030fdc6a2ba0a3306c7b61", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"properties": {"readed": {"type": "number"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/notification/test": {"post": {"tags": ["Notificaciones"], "summary": "Se crea una notificación fake para el usuario logueado", "description": "Se crea una notificación fake para el usuario logueado", "operationId": "596e390159b9511b8a2f974acf2d76d7", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle": {"get": {"tags": ["Rifas"], "summary": "Listado de rifas para los usuarios en general", "description": "Listado de rifas para los usuarios en general", "operationId": "f7c97c8934140b7d02760301e4c5b81b", "parameters": [{"name": "page", "in": "path", "description": "Número de página", "required": false}, {"name": "size", "in": "path", "description": "Tamaño de la página", "required": false}, {"name": "search", "in": "path", "description": "Campo de busqueda", "required": false}, {"name": "category", "in": "path", "description": "Id de la categoría", "required": false}, {"name": "subcategory", "in": "path", "description": "Id de la subcategoría", "required": false}, {"name": "start-price", "in": "path", "description": "<PERSON><PERSON> mínimo del bole<PERSON>", "required": false}, {"name": "end-price", "in": "path", "description": "Precio máximo del boleto", "required": false}, {"name": "order", "in": "path", "description": "Ordenar la consulta por la columna mencionada. Campos permitidos 'finish','num_tickets' y 'ticket_price'. Default 'finish'", "required": false}, {"name": "order-way", "in": "path", "description": "Ordenar la consulta por la columna mencionada en sentido ascendente o descendente. Campos permitidos 'asc' y 'desc'. Default 'desc'", "required": false}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RafflePaginated"}}}}}}, "post": {"tags": ["Rifas"], "summary": "Guardar una rifa", "description": "Guardar una rifa", "operationId": "2c17058d053683fce488db6342cec5d2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Raffle"}}}}, "412": {"description": "Invalid User | Invalid ticket price | Invalid prize value| Card active is required to create a raffle "}}, "security": [{"bearerAuth": []}]}}, "/raffle/results": {"get": {"tags": ["Rifas"], "summary": "Listado de rifas ejecutadas", "description": "Listado de rifas ejecutadas", "operationId": "df9a9959136a2dc6c5be46493e2fd64d", "parameters": [{"name": "page", "in": "path", "description": "Número de página", "required": false}, {"name": "size", "in": "path", "description": "Tamaño de la página", "required": false}, {"name": "search", "in": "path", "description": "Campo de busqueda", "required": false}, {"name": "category", "in": "path", "description": "Id de la categoría", "required": false}, {"name": "subcategory", "in": "path", "description": "Id de la subcategoría", "required": false}, {"name": "start-price", "in": "path", "description": "<PERSON><PERSON> mínimo del bole<PERSON>", "required": false}, {"name": "end-price", "in": "path", "description": "Precio máximo del boleto", "required": false}, {"name": "order", "in": "path", "description": "Ordenar la consulta por la columna mencionada. Campos permitidos 'finish','num_tickets' y 'ticket_price'. Default 'finish'", "required": false}, {"name": "order-way", "in": "path", "description": "Ordenar la consulta por la columna mencionada en sentido ascendente o descendente. Campos permitidos 'asc' y 'desc'. Default 'desc'", "required": false}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RafflePaginated"}}}}}}}, "/raffle/{id}": {"get": {"tags": ["Rifas"], "summary": "Obtener la información de la rifa", "description": "Obtener la información de la rifa", "operationId": "d6e5464546f8a9f0ccda7d1a201ebbc4", "parameters": [{"name": "id", "in": "path", "description": "Id de la rifa", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleTickets"}}}}}}, "put": {"tags": ["Rifas"], "summary": "Actualizar rifa", "description": "Actualizar rifa", "operationId": "3374ab09eb72d957f2f2d5b21f25be8c", "parameters": [{"name": "id", "in": "path", "description": "Id de la rifa", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Raffle"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle/{id}/winner-detail": {"get": {"tags": ["Rifas"], "summary": "Obtener la información de la rifa con la informacion del ganador", "description": "Obtener la información de la rifa del ganador", "operationId": "e7c4068606ad0821eb089bae9e06e8ab", "parameters": [{"name": "id", "in": "path", "description": "Id de la rifa", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinnerDetail"}}}}}}}, "/raffle/admin": {"get": {"tags": ["Rifas"], "summary": "Listado de rifas para los administradores o rifantes", "description": "Listado de rifas para los administradores o rifantes", "operationId": "a391d3d11f14159ef93afd07224462a2", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Raffle"}}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle/upload-photo": {"post": {"tags": ["Rifas"], "summary": "Subir una imagen para la rifa", "description": "Subir una imagen para la rifa", "operationId": "8ca12a4173c5b7c782728ac6e64fc92e", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["file"], "properties": {"file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle/record": {"get": {"tags": ["Rifas"], "summary": "Listado de rifas en las que el cliente ha comprado boletos", "description": "Listado de rifas en las que el cliente ha comprado boletos", "operationId": "83aac85a2b673c296445d77b7e3a13e8", "parameters": [{"name": "page", "in": "path", "description": "Número de página", "required": false}, {"name": "size", "in": "path", "description": "Tamaño de la página", "required": false}, {"name": "search", "in": "path", "description": "Campo de busqueda", "required": false}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RaffleRecordPaginated"}}}}}}}}, "/raffle/{raffle}/digital-prize": {"post": {"tags": ["Rifa", "Digital", "Premio", "Instrucciones"], "summary": "Actualiza las instrucciones parra cangear el premio", "description": "Actualiza las instrucciones parra canjear el premio", "operationId": "9091ef73e22a15fd9c2cf6e24e5670e6", "parameters": [{"name": "raffle", "in": "path", "description": "Id de la tabla raffles", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleDigitalPrizeUpdateInstructions"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleDigitalPrize"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle/{raffle}/digital-prize-evidence": {"post": {"tags": ["Rifa", "Digital", "Premio", "Evidencias"], "summary": "Agregar evidencia del premio digital", "description": "Agregar evidencia del premio digital", "operationId": "f5071e0d3505b97dfcb4bfbf92cb8267", "parameters": [{"name": "raffle", "in": "path", "description": "Id de la tabla raffles", "required": true, "example": "external | instructions | file_instructions | redeem_code"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleDigitalPrizeEvidenceAdd"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleDigitalPrizeEvidence"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle-report": {"get": {"tags": ["Reporte"], "summary": "Listado de reportes de rifas", "description": "Listado de reportes de rifas", "operationId": "e591368639646b0248a7111ee6f4bf54", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RaffleReport"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Reporte"], "summary": "Guardar un reporte", "description": "Guardar un reporte", "operationId": "8e613c8d9b7159174c81da501a762f4e", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleReportSave"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleReport"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle-report/{id}": {"get": {"tags": ["Reporte"], "summary": "Obtener información del reporte", "description": "Obtener información del reporte", "operationId": "37142b62aae095a90b36be868c9c4b76", "parameters": [{"name": "id", "in": "path", "description": "Id del reporte", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleReport"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Reporte"], "summary": "Actualizar reporte", "description": "Actualizar reporte", "operationId": "991c26eb2274924fe910b051754900f7", "parameters": [{"name": "id", "in": "path", "description": "Id del reporte", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleReportEdit"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleReport"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle-winner/{id}/update-address": {"post": {"tags": ["Ganador,Rifa"], "summary": "Actualizar dirección del ganador de la rifa", "description": "Actualizar dirección del ganador de la rifa", "operationId": "034a98234ba4eaa0267c6d913f46527d", "parameters": [{"name": "id", "in": "path", "description": "Id de la tabla raffle_winners", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinnerUpdateAddress"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinner"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle-winner/{id}/update-delivery-information": {"post": {"tags": ["Ganador,Rifa"], "summary": "Actualizar la información de entrega del premio", "description": "Actualizar la información de entrega del premio", "operationId": "ac7fa4480363c6c7f4b7d22e6aa0de7e", "parameters": [{"name": "id", "in": "path", "description": "Id de la tabla raffle_winners", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinnerUpdateDeliveryInformation"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinner"}}}}}, "security": [{"bearerAuth": []}]}}, "/raffle-winner/{id}/update-prize-status": {"post": {"tags": ["Ganador,Rifa"], "summary": "Actualizar el estatus del premio", "description": "Actualizar el estatus del premio", "operationId": "f73cf525489cf7d5d4fb2da5842c525b", "parameters": [{"name": "id", "in": "path", "description": "Id de la tabla raffle_winners", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinnerUpdatePrizeStatus"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RaffleWinner"}}}}}, "security": [{"bearerAuth": []}]}}, "/ticket/lock/{id}": {"put": {"tags": ["Boletos"], "summary": "Bloquear boleto un boleto", "description": "Bloquear un boleto", "operationId": "25c25f8bcef535806b30720c98e9cfc5", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ticket"}}}}, "412": {"description": "Ticket bloqueado actualmente o comprado"}}, "security": [{"bearerAuth": []}]}}, "/ticket/unlock/{id}": {"put": {"tags": ["Boletos"], "summary": "Bloquear boleto un boleto", "description": "Bloquear un boleto", "operationId": "f33562b4226820488450dedd397fdbe9", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Ticket"}}}}, "412": {"description": "Ticket bloqueado por otro usuario o comprado"}}, "security": [{"bearerAuth": []}]}}, "/ticket/random": {"post": {"tags": ["Boletos"], "summary": "Obtener boletos disponibles de una rifa en modo aleatorio", "description": "Obtener boletos disponibles de una rifa en modo aleatorio. Si se tienen boletos bloqueados de esta rifa se liberarán.", "operationId": "4d0c8e3edc14366d5f8f221a9353cf6b", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"raffle_id": {"type": "number"}, "number": {"type": "number"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ticket"}}}}}}, "security": [{"bearerAuth": []}]}}, "/ticket/payment/{id}": {"post": {"tags": ["Boletos"], "summary": "Pagar los boletos apartados. Actualmente no procesa pagos.", "description": "Pagar los boletos apartados. Actualmente no procesa pagos.", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ticket"}}}}}, "412": {"description": "Tickets not selected"}}, "security": [{"bearerAuth": []}]}}, "/ticket/payment/{id}/order": {"post": {"tags": ["Boletos"], "summary": "Pagar los boletos apartados de una rifa, tomando en cuenta metodo de pagos.", "description": "Pagar los boletos apartados de una rifa, tomando en cuenta metodo de pagos.", "operationId": "75bebee84254649c01f8e28a30a60b12", "parameters": [{"name": "id", "in": "path", "description": "Id de la rifa", "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"card_id": {"type": "number"}, "method": {"type": "string", "example": "Conekta | Rafiki"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Ticket"}}}}}, "412": {"description": "Tickets not selected"}}, "security": [{"bearerAuth": []}]}}, "/register": {"post": {"tags": ["Autenticación"], "summary": "Registro de un cliente", "description": "Registro de un cliente mediante correo electrónico y contraseña", "operationId": "96c6114f12ed09d2328f8b0b158734fb", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password", "name", "last_name", "nickname"], "properties": {"password": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "last_name": {"type": "string"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "code_area": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPermissions"}}}}}}}, "/user/change-password": {"post": {"tags": ["Autenticación"], "summary": "Actualizar información de un usuario", "description": "Actualizar información de un usuario", "operationId": "653fb7a1aa59928a7642a47d84c27747", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["new_password", "old_password"], "properties": {"old_password": {"type": "string"}, "new_password": {"type": "string"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearerAuth": []}]}}, "/getResend/{id}": {"get": {"tags": ["Verify"], "summary": "Obtener URL para reenviar el correo", "description": "Genera la URL con la firma para reenviar el correo de verificación.", "operationId": "562f943192978a92ca879780b126a8e8", "parameters": [{"name": "id", "in": "path", "description": "Id del usuario", "required": true}], "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/user/lock-time": {"put": {"tags": ["Boletos"], "summary": "Asignar el tiempo para el bloqueo de boletos.", "description": "Asignar el tiempo para el bloqueo de boletos. Si se envia el Id de la rifa se actualizará el tiempo de bloqueo para los boletos de la rifa.", "operationId": "8b42919473b53d8704fdc946767fe515", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"raffle_id": {"type": "number"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearerAuth": []}]}}, "/user/unlock-time": {"put": {"tags": ["Boletos"], "summary": "<PERSON><PERSON><PERSON> boletos.", "description": "Liberar boletos. Se liberan los boletos que tiene apartado el usuario. Si no se llama el servicio el boleto estará bloqueado hasta que el proceso automatico los detecte.", "operationId": "85e5024c86398e87a0633d57ce08b540", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearerAuth": []}]}}, "/profile": {"get": {"tags": ["Perfil"], "summary": "Obtener la información del perfil", "description": "Obtener la información del perfil", "operationId": "c08846d5dd388c9e0492eb50a8cf291f", "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Perfil"], "summary": "Actualizar información de un usuario", "description": "Actualizar información de un usuario", "operationId": "f7b574a8fc98e85767c241ab70b0ac1e", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"properties": {"name": {"type": "string"}, "last_name": {"type": "string"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "code_area": {"type": "string"}, "file": {"type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "Exito", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"AddressSave": {"properties": {"name": {"type": "string"}, "country": {"type": "string"}, "state": {"type": "string"}, "city": {"type": "string"}, "cp": {"type": "string"}, "code_area": {"type": "string"}, "phone": {"type": "string"}, "contact_name": {"type": "string"}}, "type": "object"}, "Address": {"allOf": [{"$ref": "#/components/schemas/AddressSave"}, {"properties": {"id": {"type": "number"}, "user_id": {"type": "number"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}]}, "ApplicationSave": {"required": ["id_front_file", "bank_information_file"], "properties": {"id_front_file": {"type": "string", "format": "binary", "example": "file"}, "id_back_file": {"type": "string", "format": "binary", "example": "file"}, "bank_information_file": {"type": "string"}, "rfc": {"type": "string", "format": "binary", "example": "file"}, "url_rfc_documentation": {"type": "string", "format": "binary", "example": "file"}}, "type": "object"}, "ApplicationEdit": {"required": ["approved", "rejection_reason"], "properties": {"approved": {"type": "boolean"}, "rejection_reason": {"type": "string"}}, "type": "object"}, "ApplicationEditRFC": {"required": ["rfc", "url_rfc_documentation"], "properties": {"rfc": {"type": "string"}, "url_rfc_documentation": {"type": "string", "format": "binary", "example": "file"}}, "type": "object"}, "Application": {"properties": {"id": {"type": "number"}, "user_id": {"type": "number"}, "admin_id": {"type": "number"}, "rfc": {"type": "string"}, "url_id_front": {"type": "string"}, "url_id_back": {"type": "string"}, "url_bank_information": {"type": "string"}, "approval": {"type": "boolean"}, "approval_date": {"type": "string", "format": "date-time"}, "rejection_reason": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "BalanceSave": {"properties": {"amount": {"type": "number"}, "points": {"type": "number"}, "commission": {"type": "number"}, "user_id": {"type": "number"}, "card_id": {"type": "number"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "Balance": {"allOf": [{"$ref": "#/components/schemas/BalanceSave"}, {"properties": {"id": {"type": "number"}}, "type": "object"}]}, "Wallet": {"properties": {"total": {"type": "number"}, "deposits": {"type": "array", "items": {"$ref": "#/components/schemas/Balance"}}, "payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOrder"}}}, "type": "object"}, "CardSave": {"properties": {"alias": {"type": "string"}, "number": {"type": "string"}, "token": {"type": "string"}, "source_id": {"type": "string"}, "type": {"type": "string"}, "active": {"type": "boolean"}}, "type": "object"}, "Card": {"allOf": [{"$ref": "#/components/schemas/CardSave"}, {"properties": {"id": {"type": "number"}, "number_formatted": {"type": "string"}, "type_formatted": {"type": "string"}}, "type": "object"}]}, "Paginated": {"properties": {"current_page": {"type": "number"}, "first_page_url": {"type": "string"}, "from": {"type": "number"}, "last_page": {"type": "number"}, "last_page_url": {"type": "string"}, "next_page_url": {"type": "string"}, "path": {"type": "string"}, "per_page": {"type": "number"}, "prev_page_url": {"type": "string"}, "to": {"type": "number"}, "total": {"type": "number"}}, "type": "object"}, "Catalog": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "type": "object"}, "Date": {"properties": {"date": {"type": "datetime"}}, "type": "object"}, "CatalogDescription": {"allOf": [{"$ref": "#/components/schemas/Catalog"}, {"properties": {"description": {"type": "string"}}, "type": "object"}]}, "Subcategory": {"allOf": [{"$ref": "#/components/schemas/CatalogDescription"}, {"properties": {"category_id": {"type": "number"}}, "type": "object"}]}, "CatalogBank": {"allOf": [{"$ref": "#/components/schemas/Catalog"}, {"properties": {"code": {"type": "string"}}, "type": "object"}]}, "NotificationSave": {"properties": {"title": {"type": "string"}, "message": {"type": "string"}, "data": {"type": "number"}, "user_id": {"type": "number"}, "player_id": {"type": "number"}, "readed": {"type": "number"}, "created_at": {"type": "date"}, "cat_notification_type_id": {"type": "number"}}, "type": "object"}, "Notification": {"allOf": [{"properties": {"id": {"type": "number"}}, "type": "object"}, {"$ref": "#/components/schemas/NotificationSave"}, {"properties": {"type": {"$ref": "#/components/schemas/Catalog"}}, "type": "object"}]}, "NotificationPaginated": {"allOf": [{"$ref": "#/components/schemas/Paginated"}, {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}, "type": "object"}]}, "PaymentOrder": {"properties": {"id": {"type": "number"}, "payment_total": {"type": "number"}, "num_tickets": {"type": "number"}, "commission": {"type": "number"}, "payment_type": {"type": "string", "example": "Conekta | Rafiki | Points"}, "status": {"type": "string", "example": " success | error"}, "raffle_id": {"type": "number"}, "buyer_id": {"type": "number"}, "card_id": {"type": "number"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "RaffleSave": {"required": ["name", "cat_state_id", "start", "finish", "prize_value", "num_tickets", "ticket_price", "active", "product_type", "subcategory_id"], "properties": {"name": {"type": "string"}, "brand": {"type": "string"}, "model": {"type": "string"}, "description": {"type": "string"}, "cat_state_id": {"type": "number"}, "city": {"type": "string"}, "start": {"type": "string", "example": "2021-04-28 12:56:29"}, "finish": {"type": "string", "example": "2021-04-28 12:56:29"}, "prize_value": {"type": "number"}, "num_tickets": {"type": "number"}, "selled_tickets": {"type": "number"}, "ticket_price": {"type": "number"}, "suggested_value": {"type": "number"}, "active": {"type": "boolean"}, "canceled": {"type": "boolean"}, "cancel_comments": {"type": "string"}, "product_type": {"type": "string", "example": " 'Physical' | 'Digital' | 'Service' "}, "prize_type": {"type": "string", "example": " 'New' | 'Used' "}, "subcategory_id": {"type": "number"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/RafflePhotoSave"}}, "tops": {"type": "array", "items": {"$ref": "#/components/schemas/Tops"}}, "report": {"$ref": "#/components/schemas/RaffleReport"}}, "type": "object"}, "Raffle": {"allOf": [{"$ref": "#/components/schemas/RaffleSave"}, {"properties": {"id": {"type": "number"}}, "type": "object"}, {"properties": {"min_percentage": {"type": "number"}}, "type": "object"}, {"properties": {"status": {"type": "string", "example": " 'pending' | 'canceled' | 'raffled' "}}, "type": "object"}]}, "RaffleWinnerDetail": {"allOf": [{"$ref": "#/components/schemas/Raffle"}, {"properties": {"raffle_winner": {"$ref": "#/components/schemas/RaffleWinner"}}, "type": "object"}, {"properties": {"digital_prize": {"$ref": "#/components/schemas/RaffleDigitalPrize"}}, "type": "object"}]}, "RafflePaginated": {"allOf": [{"$ref": "#/components/schemas/Paginated"}, {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Raffle"}}}, "type": "object"}]}, "RafflePhotoSave": {"required": ["url_image", "default"], "properties": {"url_image": {"type": "string"}, "default": {"type": "boolean"}}, "type": "object"}, "RaffleTickets": {"allOf": [{"$ref": "#/components/schemas/Raffle"}, {"properties": {"tickets": {"type": "array", "items": {"$ref": "#/components/schemas/RaffleTicket"}}, "tops": {"type": "array", "items": {"$ref": "#/components/schemas/Tops"}}}, "type": "object"}]}, "Tops": {"properties": {"total": {"type": "number"}}, "type": "object"}, "RaffleTicket": {"properties": {"number": {"type": "number"}, "locked": {"type": "boolean"}, "locked_finish": {"type": "string", "example": "2021-04-28 12:56:29"}, "status": {"type": "string", "example": " 'Free' | 'Locked' | 'Sold' "}, "raffle_id": {"type": "number"}, "buyer_id": {"type": "number"}}, "type": "object"}, "RaffleRecordPaginated": {"allOf": [{"$ref": "#/components/schemas/Paginated"}, {"properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/RaffleRecord"}}}, "type": "object"}]}, "RaffleRecord": {"allOf": [{"$ref": "#/components/schemas/Raffle"}, {"properties": {"tickets": {"type": "array", "items": {"$ref": "#/components/schemas/RaffleRecordTicket"}}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/RafflePhotoSave"}}}, "type": "object"}]}, "RaffleRecordTicket": {"properties": {"number": {"type": "number"}, "status": {"type": "string", "example": " 'Free' | 'Locked' | 'Buyed' "}, "raffle_id": {"type": "number"}, "buyer_id": {"type": "number"}, "fast_purchase": {"type": "boolean"}, "payment_type": {"type": "string", "example": " 'Conekta'| 'Ra<PERSON><PERSON>' | 'Points'  "}, "buy_date": {"type": "string", "example": "2021-04-28 12:56:29"}}, "type": "object"}, "RaffleDigitalPrize": {"properties": {"id": {"type": "number"}, "raffle_id": {"type": "number"}, "instructions": {"type": "string"}, "redeem_code": {"type": "string"}, "external_link": {"type": "string"}, "url_instructions": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "RaffleDigitalPrizeUpdateInstructions": {"required": ["instructions"], "properties": {"instructions": {"type": "string"}, "redeem_code": {"type": "string"}, "external_link": {"type": "string"}, "instructions_file": {"type": "string", "format": "binary", "example": "file"}}, "type": "object"}, "RaffleDigitalPrizeEvidence": {"properties": {"id": {"type": "number"}, "raffle_id": {"type": "number"}, "user_id": {"type": "number"}, "type": {"type": "string", "example": "external | instructions | file_instructions | redeem_code"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "RaffleDigitalPrizeEvidenceAdd": {"required": ["type"], "properties": {"type": {"type": "string"}}, "type": "object"}, "RaffleReportSave": {"required": ["raffle_id", "comments"], "properties": {"raffle_id": {"type": "number"}, "comments": {"type": "string"}}, "type": "object"}, "RaffleReportEdit": {"required": ["raffle_id", "comments"], "properties": {"status": {"type": "string"}, "admin_comments": {"type": "string"}}, "type": "object"}, "RaffleReport": {"properties": {"id": {"type": "number"}, "raffle_id": {"type": "number"}, "user_id": {"type": "number"}, "admin_id": {"type": "number"}, "status": {"type": "string"}, "comments": {"type": "string"}, "admin_comments": {"type": "string"}, "reviewed_date": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "RaffleWinner": {"properties": {"id": {"type": "number"}, "winner_id": {"type": "number"}, "raffle_id": {"type": "number"}, "ticket_id": {"type": "number"}, "address_id": {"type": "number"}, "delivery_company": {"type": "string"}, "estimated_delivery_date": {"type": "string", "format": "date-time"}, "received_prize": {"type": "number"}, "reception_date": {"type": "string", "format": "date-time"}, "evidence_url": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "winner": {"$ref": "#/components/schemas/RaffleWinnerUser"}, "ticket": {"$ref": "#/components/schemas/RaffleWinnerTicket"}, "address": {"$ref": "#/components/schemas/Address"}}, "type": "object"}, "RaffleWinnerUser": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "type": "object"}, "RaffleWinnerTicket": {"properties": {"id": {"type": "number"}, "number": {"type": "string"}}, "type": "object"}, "RaffleWinnerUpdateAddress": {"required": ["address_id"], "properties": {"address_id": {"type": "number"}}, "type": "object"}, "RaffleWinnerUpdateDeliveryInformation": {"required": ["delivery_company", "track_id", "estimated_delivery_date", "delivery_voucher_file"], "properties": {"delivery_company": {"type": "string"}, "track_id": {"type": "string"}, "track_url": {"type": "string"}, "estimated_delivery_date": {"type": "string", "format": "date-time"}, "delivery_voucher_file": {"type": "string", "format": "binary", "example": "file"}}, "type": "object"}, "RaffleWinnerUpdatePrizeStatus": {"required": ["received_prize", "reception_date", "estimated_delivery_date"], "properties": {"received_prize": {"type": "integer"}, "reception_date": {"type": "string", "format": "date-time"}, "evidence_file": {"type": "string", "format": "binary", "example": "file"}}, "type": "object"}, "Ticket": {"properties": {"id": {"type": "number"}, "number": {"type": "number"}, "locked": {"type": "boolean"}, "locked_finish": {"type": "string", "example": "2021-04-28 12:56:29"}, "buy_date": {"type": "string", "example": "2021-04-28 12:56:29"}, "fast_purchase": {"type": "boolean"}, "status": {"type": "string", "example": " 'Free' | 'Locked' | 'Sold' "}, "payment_type": {"type": "string", "example": " 'Conekta'| 'Ra<PERSON><PERSON>' | 'Points'  "}, "raffle_id": {"type": "number"}, "buyer_id": {"type": "number"}}, "type": "object"}, "User": {"properties": {"id": {"type": "number"}, "name": {"type": "string"}, "last_name": {"type": "string"}, "nick_name": {"type": "string"}, "email": {"type": "string"}, "url_photo": {"type": "string"}, "created_at": {"type": "string", "example": "2021-04-28 12:56:29"}, "updated_at": {"type": "string", "example": "2021-04-28 12:56:29"}, "ticket_locked_finish": {"type": "string", "example": "2021-04-28 12:56:29"}, "role": {"type": "string"}}, "type": "object"}, "UserPermissions": {"allOf": [{"$ref": "#/components/schemas/User"}, {"properties": {"role": {"type": "string"}, "permission": {"type": "array", "items": {"type": "string"}}}, "type": "object"}]}, "UserPermissionsToken": {"properties": {"user": {"$ref": "#/components/schemas/UserPermissions"}, "access_token": {"type": "string"}}, "type": "object"}}}}